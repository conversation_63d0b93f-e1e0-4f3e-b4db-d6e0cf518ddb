<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.facishare</groupId>
    <artifactId>fs-bpm</artifactId>
    <version>9.6.0-SNAPSHOT</version>
    <modules>
        <module>fs-bpm-core</module>
        <module>fs-bpm-biz</module>
        <module>fs-bpm-common</module>
        <module>fs-bpm-console</module>
        <module>fs-bpm-processor</module>
        <module>fs-bpm-processor-core</module>
        <module>fs-bpm-biz-core</module>
        <module>fs-bpm-cloud</module>
        <module>fs-bpm-api</module>
    </modules>
    <properties>
        <reversion>9.6.0-SNAPSHOT</reversion>
        <fs-fsi-proxy.version>3.0.0-SNAPSHOT</fs-fsi-proxy.version>
        <spring-webmvc.version>4.1.6.RELEASE</spring-webmvc.version>
        <fs-metadata-service.version>1.0-SNAPSHOT</fs-metadata-service.version>
        <fs-flow-public.version>9.6.0-SNAPSHOT</fs-flow-public.version>

        <!-- SonarQube配置 - 兼容Java 11 -->
        <sonar.maven.plugin.version>3.9.1.2184</sonar.maven.plugin.version>
        <sonar.java.source>17</sonar.java.source>
        <sonar.java.target>17</sonar.java.target>
        <sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>

    </properties>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>1.10.19</version>
                <scope>test</scope>
            </dependency>

            <!-- CGLib和ASM版本兼容性管理 -->
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>3.3.0</version>
            </dependency>
            <!-- 强制使用兼容CGLib 3.3.0的ASM版本 -->
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>7.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-commons</artifactId>
                <version>7.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-tree</artifactId>
                <version>7.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-util</artifactId>
                <version>7.3.1</version>
            </dependency>

            <!-- ByteBuddy版本管理，避免与CGLib冲突 -->
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.12.23</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>1.12.23</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar.maven.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.6.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
