<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-bpm</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-bpm-cloud</artifactId>
    <packaging>war</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sonar.skip>true</sonar.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bpm-biz-core</artifactId>
            <version>${reversion}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bpm-processor-core</artifactId>
            <version>${reversion}</version>
        </dependency>
    </dependencies>

</project>