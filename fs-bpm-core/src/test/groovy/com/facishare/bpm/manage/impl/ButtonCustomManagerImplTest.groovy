package com.facishare.bpm.manage.impl

import com.facishare.bpm.handler.task.button.TaskButtonHandler
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import org.springframework.context.ApplicationContext
import spock.lang.Specification

/**
 * Test for ButtonCustomManagerImpl
 */
class ButtonCustomManagerImplTest extends Specification {

    def buttonCustomManager = new ButtonCustomManagerImpl()
    def applicationContext = Mock(ApplicationContext)
    def taskButtonHandler1 = Mock(TaskButtonHandler)
    def taskButtonHandler2 = Mock(TaskButtonHandler)

    def setup() {
        buttonCustomManager.setApplicationContext(applicationContext)
        
        // Mock task button handlers
        taskButtonHandler1.getTaskType() >> ExecutionTypeEnum.update
        taskButtonHandler2.getTaskType() >> ExecutionTypeEnum.approve
    }

    def "test init method initializes handlers correctly"() {
        given: "application context with task button handlers"
        def handlerBeans = [
            "handler1": taskButtonHandler1,
            "handler2": taskButtonHandler2
        ]
        applicationContext.getBeansOfType(TaskButtonHandler.class) >> handlerBeans

        when: "init method is called"
        buttonCustomManager.init()

        then: "handlers map is populated correctly"
        buttonCustomManager.getHandler(ExecutionTypeEnum.update) == taskButtonHandler1
        buttonCustomManager.getHandler(ExecutionTypeEnum.approve) == taskButtonHandler2
    }

    def "test getHandler returns correct handler for execution type"() {
        given: "initialized button custom manager"
        def handlerBeans = [
            "handler1": taskButtonHandler1,
            "handler2": taskButtonHandler2
        ]
        applicationContext.getBeansOfType(TaskButtonHandler.class) >> handlerBeans
        buttonCustomManager.init()

        when: "getting handler for specific execution type"
        def result = buttonCustomManager.getHandler(ExecutionTypeEnum.update)

        then: "correct handler is returned"
        result == taskButtonHandler1
    }

    def "test getHandler returns null for unknown execution type"() {
        given: "initialized button custom manager"
        def handlerBeans = [
            "handler1": taskButtonHandler1
        ]
        applicationContext.getBeansOfType(TaskButtonHandler.class) >> handlerBeans
        buttonCustomManager.init()

        when: "getting handler for unknown execution type"
        def result = buttonCustomManager.getHandler(ExecutionTypeEnum.operation)

        then: "null is returned"
        result == null
    }

    def "test init with empty handlers map"() {
        given: "application context with no task button handlers"
        applicationContext.getBeansOfType(TaskButtonHandler.class) >> [:]

        when: "init method is called"
        buttonCustomManager.init()

        then: "handlers map is empty"
        buttonCustomManager.getHandler(ExecutionTypeEnum.update) == null
        buttonCustomManager.getHandler(ExecutionTypeEnum.approve) == null
    }

    def "test init with duplicate execution types"() {
        given: "application context with handlers having same execution type"
        def duplicateHandler = Mock(TaskButtonHandler)
        duplicateHandler.getTaskType() >> ExecutionTypeEnum.update
        
        def handlerBeans = [
            "handler1": taskButtonHandler1,
            "duplicate": duplicateHandler
        ]
        applicationContext.getBeansOfType(TaskButtonHandler.class) >> handlerBeans

        when: "init method is called"
        buttonCustomManager.init()

        then: "IllegalStateException is thrown due to duplicate keys"
        thrown(IllegalStateException)
    }
}
