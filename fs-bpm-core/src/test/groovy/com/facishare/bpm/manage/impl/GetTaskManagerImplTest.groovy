package com.facishare.bpm.manage.impl

import com.facishare.bpm.handler.task.detail.GetTaskDetailHandler
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import org.springframework.context.ApplicationContext
import spock.lang.Specification

/**
 * Test for GetTaskManagerImpl
 */
class GetTaskManagerImplTest extends Specification {

    def getTaskManager = new GetTaskManagerImpl()
    def applicationContext = Mock(ApplicationContext)
    def taskDetailHandler1 = Mock(GetTaskDetailHandler)
    def taskDetailHandler2 = Mock(GetTaskDetailHandler)

    def setup() {
        getTaskManager.setApplicationContext(applicationContext)
        
        // Mock task detail handlers
        taskDetailHandler1.getTaskType() >> ExecutionTypeEnum.update
        taskDetailHandler2.getTaskType() >> ExecutionTypeEnum.approve
    }

    def "test init method initializes handlers correctly"() {
        given: "application context with task detail handlers"
        def handlerBeans = [
            "handler1": taskDetailHandler1,
            "handler2": taskDetailHandler2
        ]
        applicationContext.getBeansOfType(GetTaskDetailHandler.class) >> handlerBeans

        when: "init method is called"
        getTaskManager.init()

        then: "handlers map is populated correctly"
        getTaskManager.getHandler(ExecutionTypeEnum.update) == taskDetailHandler1
        getTaskManager.getHandler(ExecutionTypeEnum.approve) == taskDetailHandler2
    }

    def "test getHandler returns correct handler for execution type"() {
        given: "initialized get task manager"
        def handlerBeans = [
            "handler1": taskDetailHandler1,
            "handler2": taskDetailHandler2
        ]
        applicationContext.getBeansOfType(GetTaskDetailHandler.class) >> handlerBeans
        getTaskManager.init()

        when: "getting handler for specific execution type"
        def result = getTaskManager.getHandler(ExecutionTypeEnum.update)

        then: "correct handler is returned"
        result == taskDetailHandler1
    }

    def "test getHandler returns null for unknown execution type"() {
        given: "initialized get task manager"
        def handlerBeans = [
            "handler1": taskDetailHandler1
        ]
        applicationContext.getBeansOfType(GetTaskDetailHandler.class) >> handlerBeans
        getTaskManager.init()

        when: "getting handler for unknown execution type"
        def result = getTaskManager.getHandler(ExecutionTypeEnum.operation)

        then: "null is returned"
        result == null
    }

    def "test init with empty handlers map"() {
        given: "application context with no task detail handlers"
        applicationContext.getBeansOfType(GetTaskDetailHandler.class) >> [:]

        when: "init method is called"
        getTaskManager.init()

        then: "handlers map is empty"
        getTaskManager.getHandler(ExecutionTypeEnum.update) == null
        getTaskManager.getHandler(ExecutionTypeEnum.approve) == null
    }

    def "test init with multiple handlers of same type"() {
        given: "application context with handlers having same execution type"
        def anotherHandler = Mock(GetTaskDetailHandler)
        anotherHandler.getTaskType() >> ExecutionTypeEnum.update
        
        def handlerBeans = [
            "handler1": taskDetailHandler1,
            "another": anotherHandler
        ]
        applicationContext.getBeansOfType(GetTaskDetailHandler.class) >> handlerBeans

        when: "init method is called"
        getTaskManager.init()

        then: "IllegalStateException is thrown due to duplicate keys"
        thrown(IllegalStateException)
    }

    def "test getHandler with all execution types"() {
        given: "handlers for different execution types"
        def updateHandler = Mock(GetTaskDetailHandler)
        def approveHandler = Mock(GetTaskDetailHandler)
        def operationHandler = Mock(GetTaskDetailHandler)
        
        updateHandler.getTaskType() >> ExecutionTypeEnum.update
        approveHandler.getTaskType() >> ExecutionTypeEnum.approve
        operationHandler.getTaskType() >> ExecutionTypeEnum.operation
        
        def handlerBeans = [
            "update": updateHandler,
            "approve": approveHandler,
            "operation": operationHandler
        ]
        applicationContext.getBeansOfType(GetTaskDetailHandler.class) >> handlerBeans
        getTaskManager.init()

        expect: "correct handlers are returned for each type"
        getTaskManager.getHandler(ExecutionTypeEnum.update) == updateHandler
        getTaskManager.getHandler(ExecutionTypeEnum.approve) == approveHandler
        getTaskManager.getHandler(ExecutionTypeEnum.operation) == operationHandler
    }
}
