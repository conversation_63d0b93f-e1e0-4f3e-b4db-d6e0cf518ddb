package com.facishare.bpm.manage.impl

import com.facishare.bpm.handler.task.data.TaskDataHandler
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import org.springframework.context.ApplicationContext
import spock.lang.Specification

/**
 * Test for TaskDataManagerImpl
 */
class TaskDataManagerImplTest extends Specification {

    def taskDataManager = new TaskDataManagerImpl()
    def applicationContext = Mock(ApplicationContext)
    def taskDataHandler1 = Mock(TaskDataHandler)
    def taskDataHandler2 = Mock(TaskDataHandler)

    def setup() {
        taskDataManager.setApplicationContext(applicationContext)
        
        // Mock task data handlers
        taskDataHandler1.getTaskType() >> ExecutionTypeEnum.update
        taskDataHandler2.getTaskType() >> ExecutionTypeEnum.approve
    }

    def "test init method initializes handlers correctly"() {
        given: "application context with task data handlers"
        def handlerBeans = [
            "handler1": taskDataHandler1,
            "handler2": taskDataHandler2
        ]
        applicationContext.getBeansOfType(TaskDataHandler.class) >> handlerBeans

        when: "init method is called"
        taskDataManager.init()

        then: "handlers map is populated correctly"
        taskDataManager.getHandler(ExecutionTypeEnum.update) == taskDataHandler1
        taskDataManager.getHandler(ExecutionTypeEnum.approve) == taskDataHandler2
    }

    def "test getHandler returns correct handler for execution type"() {
        given: "initialized task data manager"
        def handlerBeans = [
            "handler1": taskDataHandler1,
            "handler2": taskDataHandler2
        ]
        applicationContext.getBeansOfType(TaskDataHandler.class) >> handlerBeans
        taskDataManager.init()

        when: "getting handler for specific execution type"
        def result = taskDataManager.getHandler(ExecutionTypeEnum.update)

        then: "correct handler is returned"
        result == taskDataHandler1
    }

    def "test getHandler returns null for unknown execution type"() {
        given: "initialized task data manager"
        def handlerBeans = [
            "handler1": taskDataHandler1
        ]
        applicationContext.getBeansOfType(TaskDataHandler.class) >> handlerBeans
        taskDataManager.init()

        when: "getting handler for unknown execution type"
        def result = taskDataManager.getHandler(ExecutionTypeEnum.operation)

        then: "null is returned"
        result == null
    }

    def "test init with empty handlers map"() {
        given: "application context with no task data handlers"
        applicationContext.getBeansOfType(TaskDataHandler.class) >> [:]

        when: "init method is called"
        taskDataManager.init()

        then: "handlers map is empty"
        taskDataManager.getHandler(ExecutionTypeEnum.update) == null
        taskDataManager.getHandler(ExecutionTypeEnum.approve) == null
    }

    def "test generic type handling"() {
        given: "initialized task data manager with generic handlers"
        def handlerBeans = [
            "handler1": taskDataHandler1
        ]
        applicationContext.getBeansOfType(TaskDataHandler.class) >> handlerBeans
        taskDataManager.init()

        when: "getting handler with generic types"
        def result = taskDataManager.<String, Integer>getHandler(ExecutionTypeEnum.update)

        then: "correct handler is returned with proper generic types"
        result == taskDataHandler1
    }

    def "test init with multiple execution types"() {
        given: "handlers for different execution types"
        def updateHandler = Mock(TaskDataHandler)
        def approveHandler = Mock(TaskDataHandler)
        def operationHandler = Mock(TaskDataHandler)
        def addRelatedObjectHandler = Mock(TaskDataHandler)
        
        updateHandler.getTaskType() >> ExecutionTypeEnum.update
        approveHandler.getTaskType() >> ExecutionTypeEnum.approve
        operationHandler.getTaskType() >> ExecutionTypeEnum.operation
        addRelatedObjectHandler.getTaskType() >> ExecutionTypeEnum.addRelatedObject
        
        def handlerBeans = [
            "update": updateHandler,
            "approve": approveHandler,
            "operation": operationHandler,
            "addRelated": addRelatedObjectHandler
        ]
        applicationContext.getBeansOfType(TaskDataHandler.class) >> handlerBeans

        when: "init method is called"
        taskDataManager.init()

        then: "all handlers are correctly mapped"
        taskDataManager.getHandler(ExecutionTypeEnum.update) == updateHandler
        taskDataManager.getHandler(ExecutionTypeEnum.approve) == approveHandler
        taskDataManager.getHandler(ExecutionTypeEnum.operation) == operationHandler
        taskDataManager.getHandler(ExecutionTypeEnum.addRelatedObject) == addRelatedObjectHandler
    }

    def "test handler replacement with duplicate types"() {
        given: "multiple handlers with same execution type"
        def firstHandler = Mock(TaskDataHandler)
        def secondHandler = Mock(TaskDataHandler)
        
        firstHandler.getTaskType() >> ExecutionTypeEnum.update
        secondHandler.getTaskType() >> ExecutionTypeEnum.update
        
        def handlerBeans = [
            "first": firstHandler,
            "second": secondHandler
        ]
        applicationContext.getBeansOfType(TaskDataHandler.class) >> handlerBeans

        when: "init method is called"
        taskDataManager.init()

        then: "IllegalStateException is thrown due to duplicate keys"
        thrown(IllegalStateException)
    }
}
