package com.facishare.bpm.event.model

import com.facishare.bpm.RefServiceManager
import spock.lang.Specification

/**
 * Test for TaskStateIncorrectEvent
 */
class TaskStateIncorrectEventTest extends Specification {

    def serviceManager = Mock(RefServiceManager)

    def "test TaskStateIncorrectEvent creation with all parameters"() {
        given: "event parameters"
        def instanceId = "instance123"
        def reason = "Task state is incorrect"

        when: "creating TaskStateIncorrectEvent"
        def event = new TaskStateIncorrectEvent(instanceId, reason)

        then: "all properties are set correctly"
        event.instanceId == instanceId
        event.reason == reason
    }

    def "test TaskStateIncorrectEvent creation with null parameters"() {
        when: "creating TaskStateIncorrectEvent with null parameters"
        def event = new TaskStateIncorrectEvent(null, null)

        then: "all properties are null"
        event.instanceId == null
        event.reason == null
    }

    def "test TaskStateIncorrectEvent creation with empty strings"() {
        when: "creating TaskStateIncorrectEvent with empty strings"
        def event = new TaskStateIncorrectEvent("", "")

        then: "all properties are empty strings"
        event.instanceId == ""
        event.reason == ""
    }

    def "test TaskStateIncorrectEvent equality"() {
        given: "two events with same parameters"
        def event1 = new TaskStateIncorrectEvent("instance1", "reason1")
        def event2 = new TaskStateIncorrectEvent("instance1", "reason1")

        expect: "events should be equal if they have same content"
        // Note: This test assumes the class implements equals() method
        // If not implemented, this test will verify object identity
        event1.instanceId == event2.instanceId
        event1.reason == event2.reason
    }

    def "test TaskStateIncorrectEvent with special characters"() {
        given: "event parameters with special characters"
        def instanceId = "instance-123_test@456#test"
        def reason = "Error: Task state is incorrect! Please check \$special characters."

        when: "creating TaskStateIncorrectEvent"
        def event = new TaskStateIncorrectEvent(instanceId, reason)

        then: "all properties handle special characters correctly"
        event.instanceId == instanceId
        event.reason == reason
    }

    def "test TaskStateIncorrectEvent with long strings"() {
        given: "event parameters with long strings"
        def longString = "a" * 1000
        def instanceId = "instance_" + longString
        def reason = "Error message: " + longString

        when: "creating TaskStateIncorrectEvent"
        def event = new TaskStateIncorrectEvent(instanceId, reason)

        then: "all properties handle long strings correctly"
        event.instanceId == instanceId
        event.reason == reason
    }

    def "test TaskStateIncorrectEvent toString method"() {
        given: "a TaskStateIncorrectEvent"
        def event = new TaskStateIncorrectEvent("instance123", "error message")

        when: "calling toString"
        def result = event.toString()

        then: "toString returns a string representation"
        result != null
        result instanceof String
        // The exact format depends on the implementation
        // This test just ensures toString doesn't throw an exception
    }

    def "test TaskStateIncorrectEvent create static method"() {
        given: "parameters for static create method"
        def instanceId = "instance123"
        def reason = "Task state error"

        when: "creating event using static method"
        def event = TaskStateIncorrectEvent.create(serviceManager, instanceId, reason)

        then: "event is created correctly"
        event.instanceId == instanceId
        event.reason == reason
        event.serviceManager == serviceManager
    }
}
