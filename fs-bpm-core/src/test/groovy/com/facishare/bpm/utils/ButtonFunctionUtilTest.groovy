package com.facishare.bpm.utils

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.paas.engine.bpm.TaskState
import com.facishare.bpm.util.verifiy.TaskType
import spock.lang.Specification

/**
 * ButtonFunctionUtil 单元测试
 */
class ButtonFunctionUtilTest extends Specification {

    def "test isNeedChangeBPMApprover with various scenarios"() {
        when: "测试更换BPM审批人权限"
        def result = ButtonFunctionUtil.isNeedChangeBPMApprover(
                hasChangeBPMApproverPrivilege,
                isInProgressTaskState,
                isErrorTaskState,
                isExecutionState,
                isLinkAppNode,
                isUpstreamTenant,
                isSupportTaskType)

        then: "返回预期结果"
        result == expectedResult

        where: "测试各种场景组合"
        hasChangeBPMApproverPrivilege | isInProgressTaskState | isErrorTaskState | isExecutionState | isLinkAppNode | isUpstreamTenant | isSupportTaskType || expectedResult
        // 不支持的任务类型
        true                         | true                  | false            | false            | true          | true             | false             || false
        // 上游租户场景
        true                         | true                  | false            | false            | true          | true             | true              || true
        true                         | false                 | true             | false            | true          | true             | true              || true
        true                         | true                  | true             | true             | true          | true             | true              || false // 后动作异常
        false                        | true                  | false            | false            | true          | true             | true              || false // 无权限
        // 下游租户场景
        true                         | true                  | false            | false            | true          | false            | true              || true
        true                         | true                  | false            | false            | false         | false            | true              || false // 非互联节点
        true                         | false                 | false            | false            | true          | false            | true              || false // 任务非进行中
        true                         | true                  | false            | true             | true          | false            | true              || false // 后动作异常
        false                        | true                  | false            | false            | true          | false            | true              || false // 无权限
    }

    def "test isNeedStopBPM with various scenarios"() {
        when: "测试终止BPM权限"
        def result = ButtonFunctionUtil.isNeedStopBPM(isStarting, hasStopBpmPrivilege)

        then: "返回预期结果"
        result == expectedResult

        where: "测试各种场景组合"
        isStarting | hasStopBpmPrivilege || expectedResult
        true       | true                || true
        true       | false               || false
        false      | true                || false
        false      | false               || false
    }

    def "test isNeedAddTag with basic scenarios"() {
        given: "创建任务对象"
        def task = new Task()
        task.setCompleted(false)
        task.setCandidateIds(["user1"])
        task.setState(TaskState.in_progress)
        task.setNodeType("normal")
        task.setTaskType(TaskType.anyone.name())

        // 使用bpmExtension设置executionType
        def bpmExtension = [:]
        bpmExtension.put("executionType", "approve")
        task.setBpmExtension(bpmExtension)

        when: "测试加签权限"
        def result = ButtonFunctionUtil.isNeedAddTag("user1", task)

        then: "返回预期结果"
        result == true
    }

    def "test isNeedAddTag with null user"() {
        given: "创建任务对象"
        def task = new Task()
        task.setCompleted(false)
        task.setCandidateIds(["user1"])
        task.setState(TaskState.in_progress)

        when: "测试空用户加签权限"
        def result = ButtonFunctionUtil.isNeedAddTag(null, task)

        then: "返回false"
        result == false
    }

    def "test isNeedAddTag with null task"() {
        when: "测试空任务加签权限"
        def result = ButtonFunctionUtil.isNeedAddTag("user1", null)

        then: "返回false"
        result == false
    }

    def "test isNeedAddTag with completed task"() {
        given: "创建已完成的任务对象"
        def task = new Task()
        task.setCompleted(true)
        task.setCandidateIds(["user1"])
        task.setState(TaskState.in_progress)

        when: "测试已完成任务的加签权限"
        def result = ButtonFunctionUtil.isNeedAddTag("user1", task)

        then: "返回false"
        result == false
    }

    def "test isNeedAddTag with user not in candidates"() {
        given: "创建任务对象"
        def task = new Task()
        task.setCompleted(false)
        task.setCandidateIds(["user1"])
        task.setState(TaskState.in_progress)

        when: "测试不在候选人中的用户加签权限"
        def result = ButtonFunctionUtil.isNeedAddTag("user2", task)

        then: "返回false"
        result == false
    }

    def "test canRemindTask with null parameters"() {
        when: "测试空参数"
        def result = ButtonFunctionUtil.canRemindTask(null, null)

        then: "返回false"
        result == false
    }

    def "test canRemindTask with various scenarios"() {
        given: "创建服务管理器Mock"
        def serviceManager = Mock(RefServiceManager)
        serviceManager.isAdmin() >> isAdmin
        serviceManager.getUserId() >> currentUserId

        when: "测试提醒任务权限"
        def task = new Task()
        task.setState(taskState)
        task.setCandidateIds(candidateIds)
        task.setApplicantId(applicantId)

        def result = ButtonFunctionUtil.canRemindTask(serviceManager, task)

        then: "返回预期结果"
        result == expectedResult

        where: "测试各种场景组合"
        taskState             | candidateIds | applicantId | currentUserId | isAdmin || expectedResult
        // 基本无效场景
        TaskState.pass        | ["user1"]    | "user1"     | "user1"       | false   || false // 非进行中状态
        TaskState.cancel      | ["user1"]    | "user1"     | "user1"       | false   || false // 取消状态
        TaskState.error       | ["user1"]    | "user1"     | "user1"       | false   || false // 错误状态
        // 候选人检查
        TaskState.in_progress | null         | "user1"     | "user1"       | false   || false // 空候选人
        TaskState.in_progress | []           | "user1"     | "user1"       | false   || false // 空候选人列表
        // 权限检查 - 管理员
        TaskState.in_progress | ["user1"]    | "user2"     | "user3"       | true    || true  // 管理员可以提醒
        // 权限检查 - 申请人
        TaskState.in_progress | ["user1"]    | "user1"     | "user1"       | false   || true  // 申请人可以提醒
        // 权限检查 - 无权限用户
        TaskState.in_progress | ["user1"]    | "user2"     | "user3"       | false   || false // 非管理员非申请人
        // 正常场景
        TaskState.in_progress | ["user1", "user2"] | "user1" | "user1"   | false   || true  // 申请人，多候选人
        TaskState.in_progress | ["user1"]    | "user2"     | "admin"       | true    || true  // 管理员，单候选人
    }
}