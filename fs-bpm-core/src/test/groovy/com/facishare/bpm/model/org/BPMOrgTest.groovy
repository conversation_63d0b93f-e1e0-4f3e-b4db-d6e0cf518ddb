package com.facishare.bpm.model.org

import spock.lang.Specification

/**
 * Test for BPMOrg
 */
class BPMOrgTest extends Specification {

    def "test BPMOrg creation"() {
        when: "creating BPMOrg"
        def bpmOrg = new BPMOrg()

        then: "bpmOrg should be created successfully"
        bpmOrg != null
        bpmOrg instanceof BPMOrg
    }

    def "test BPMOrg setters and getters"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()
        def personMap = [1: null, 2: null]
        def crmGroupMap = ["group1": null, "group2": null]
        def roleMap = ["role1": null, "role2": null]
        def deptMap = [10: null, 20: null]
        def externalPersonMap = [100: null, 200: null]
        def externalRoleMap = ["extRole1": null, "extRole2": null]

        when: "setting properties"
        bpmOrg.setPerson(personMap)
        bpmOrg.setCRMGroup(crmGroupMap)
        bpmOrg.setRole(roleMap)
        bpmOrg.setDept(deptMap)
        bpmOrg.setExternalPerson(externalPersonMap)
        bpmOrg.setExternalRole(externalRoleMap)

        then: "properties should be set correctly"
        bpmOrg.getPerson() == personMap
        bpmOrg.getCRMGroup() == crmGroupMap
        bpmOrg.getRole() == roleMap
        bpmOrg.getDept() == deptMap
        bpmOrg.getExternalPerson() == externalPersonMap
        bpmOrg.getExternalRole() == externalRoleMap
    }

    def "test BPMOrg with null values"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()

        when: "setting null values"
        bpmOrg.setPerson(null)
        bpmOrg.setCRMGroup(null)
        bpmOrg.setRole(null)
        bpmOrg.setDept(null)
        bpmOrg.setExternalPerson(null)
        bpmOrg.setExternalRole(null)

        then: "null values should be accepted"
        bpmOrg.getPerson() == null
        bpmOrg.getCRMGroup() == null
        bpmOrg.getRole() == null
        bpmOrg.getDept() == null
        bpmOrg.getExternalPerson() == null
        bpmOrg.getExternalRole() == null
    }

    def "test BPMOrg with empty maps"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()

        when: "setting empty maps"
        bpmOrg.setPerson([:])
        bpmOrg.setCRMGroup([:])
        bpmOrg.setRole([:])
        bpmOrg.setDept([:])
        bpmOrg.setExternalPerson([:])
        bpmOrg.setExternalRole([:])

        then: "empty maps should be accepted"
        bpmOrg.getPerson().isEmpty()
        bpmOrg.getCRMGroup().isEmpty()
        bpmOrg.getRole().isEmpty()
        bpmOrg.getDept().isEmpty()
        bpmOrg.getExternalPerson().isEmpty()
        bpmOrg.getExternalRole().isEmpty()
    }

    def "test BPMOrg toString"() {
        given: "a BPMOrg with data"
        def bpmOrg = new BPMOrg()
        bpmOrg.setPerson([1: null])

        when: "calling toString"
        def result = bpmOrg.toString()

        then: "toString should return a string"
        result != null
        result instanceof String
    }

    def "test BPMOrg equals and hashCode"() {
        given: "two BPMOrg instances"
        def bpmOrg1 = new BPMOrg()
        def bpmOrg2 = new BPMOrg()

        when: "setting same properties"
        bpmOrg1.setPerson([1: null])
        bpmOrg2.setPerson([1: null])

        then: "objects should have consistent equals and hashCode behavior"
        bpmOrg1.equals(bpmOrg1) // reflexive
        bpmOrg1.hashCode() == bpmOrg1.hashCode() // consistent
    }

    def "test BPMOrg with different map types"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()

        when: "setting different map implementations"
        bpmOrg.setPerson(new HashMap<Integer, Object>())
        bpmOrg.setCRMGroup(new LinkedHashMap<String, Object>())
        bpmOrg.setRole(new TreeMap<String, Object>())

        then: "different map types should be accepted"
        bpmOrg.getPerson() instanceof Map
        bpmOrg.getCRMGroup() instanceof Map
        bpmOrg.getRole() instanceof Map
    }

    def "test BPMOrg with large maps"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()
        def largePersonMap = [:]
        def largeDeptMap = [:]

        when: "creating large maps"
        (1..1000).each { i ->
            largePersonMap[i] = null
            largeDeptMap[i] = null
        }
        bpmOrg.setPerson(largePersonMap)
        bpmOrg.setDept(largeDeptMap)

        then: "large maps should be handled correctly"
        bpmOrg.getPerson().size() == 1000
        bpmOrg.getDept().size() == 1000
    }

    def "test BPMOrg property chaining"() {
        when: "chaining property setters"
        def bpmOrg = new BPMOrg()
        bpmOrg.setPerson([1: null])
        bpmOrg.setRole(["admin": null])
        bpmOrg.setDept([10: null])

        then: "all properties should be set"
        bpmOrg.getPerson().containsKey(1)
        bpmOrg.getRole().containsKey("admin")
        bpmOrg.getDept().containsKey(10)
    }

    def "test BPMOrg default values"() {
        when: "creating new BPMOrg"
        def bpmOrg = new BPMOrg()

        then: "default values should be null"
        bpmOrg.getPerson() == null
        bpmOrg.getCRMGroup() == null
        bpmOrg.getRole() == null
        bpmOrg.getDept() == null
        bpmOrg.getExternalPerson() == null
        bpmOrg.getExternalRole() == null
    }

    def "test BPMOrg with mixed key types"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()

        when: "setting maps with different key types"
        bpmOrg.setPerson([1: null, 2: null]) // Integer keys
        bpmOrg.setCRMGroup(["group1": null, "group2": null]) // String keys
        bpmOrg.setDept([100: null, 200: null]) // Integer keys

        then: "different key types should be handled correctly"
        bpmOrg.getPerson().keySet().every { it instanceof Integer }
        bpmOrg.getCRMGroup().keySet().every { it instanceof String }
        bpmOrg.getDept().keySet().every { it instanceof Integer }
    }

    def "test BPMOrg map operations"() {
        given: "a BPMOrg instance with maps"
        def bpmOrg = new BPMOrg()
        def personMap = [1: null, 2: null]
        def roleMap = ["admin": null, "user": null]

        when: "setting and modifying maps"
        bpmOrg.setPerson(personMap)
        bpmOrg.setRole(roleMap)

        then: "map operations should work correctly"
        bpmOrg.getPerson().size() == 2
        bpmOrg.getRole().size() == 2
        bpmOrg.getPerson().containsKey(1)
        bpmOrg.getRole().containsKey("admin")
    }

    def "test BPMOrg with special string keys"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()

        when: "setting maps with special string keys"
        bpmOrg.setCRMGroup(["<EMAIL>": null, "group#123": null])
        bpmOrg.setRole(["role\$admin": null, "role&user": null])

        then: "special characters in keys should be handled correctly"
        bpmOrg.getCRMGroup().containsKey("<EMAIL>")
        bpmOrg.getCRMGroup().containsKey("group#123")
        bpmOrg.getRole().containsKey("role\$admin")
        bpmOrg.getRole().containsKey("role&user")
    }

    def "test BPMOrg map immutability"() {
        given: "a BPMOrg instance"
        def bpmOrg = new BPMOrg()
        def originalMap = [1: null, 2: null]

        when: "setting a map and then modifying the original"
        bpmOrg.setPerson(originalMap)
        originalMap[3] = null

        then: "BPMOrg should reference the same map object"
        bpmOrg.getPerson().containsKey(3) // Map is not copied, same reference
    }
}
