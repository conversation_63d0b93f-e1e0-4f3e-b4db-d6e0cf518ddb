package com.facishare.bpm.model

import com.facishare.bpm.model.paas.engine.bpm.InstanceState
import spock.lang.Specification

/**
 * Test for InstanceOutline
 */
class InstanceOutlineTest extends Specification {

    def "test InstanceOutline creation"() {
        when: "creating InstanceOutline"
        def outline = new InstanceOutline()

        then: "outline should be created successfully"
        outline != null
        outline instanceof InstanceOutline
    }

    def "test InstanceOutline setters and getters"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting properties"
        outline.setId("instance123")
        outline.setEntityId("entity456")
        outline.setObjectId("object789")
        outline.setWorkflowId("workflow101")
        outline.setWorkflowName("Test Workflow")
        outline.setStartTime(1234567890L)
        outline.setEndTime(1234567999L)
        outline.setState(InstanceState.in_progress)

        then: "properties should be set correctly"
        outline.getId() == "instance123"
        outline.getEntityId() == "entity456"
        outline.getObjectId() == "object789"
        outline.getWorkflowId() == "workflow101"
        outline.getWorkflowName() == "Test Workflow"
        outline.getStartTime() == 1234567890L
        outline.getEndTime() == 1234567999L
        outline.getState() == InstanceState.in_progress
    }

    def "test InstanceOutline with null values"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting null values"
        outline.setId(null)
        outline.setEntityId(null)
        outline.setObjectId(null)
        outline.setWorkflowId(null)
        outline.setWorkflowName(null)
        outline.setStartTime(null)
        outline.setEndTime(null)
        outline.setState(null)

        then: "null values should be accepted"
        outline.getId() == null
        outline.getEntityId() == null
        outline.getObjectId() == null
        outline.getWorkflowId() == null
        outline.getWorkflowName() == null
        outline.getStartTime() == null
        outline.getEndTime() == null
        outline.getState() == null
    }

    def "test InstanceOutline with empty strings"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting empty string values"
        outline.setId("")
        outline.setEntityId("")
        outline.setObjectId("")
        outline.setWorkflowId("")
        outline.setWorkflowName("")

        then: "empty strings should be accepted"
        outline.getId() == ""
        outline.getEntityId() == ""
        outline.getObjectId() == ""
        outline.getWorkflowId() == ""
        outline.getWorkflowName() == ""
    }

    def "test InstanceOutline with different instance states"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting different instance states"
        def states = [
            InstanceState.in_progress,
            InstanceState.pass,
            InstanceState.error
        ]

        then: "all instance states should be accepted"
        states.each { state ->
            outline.setState(state)
            assert outline.getState() == state
        }
    }

    def "test InstanceOutline with Long time values"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()
        def startTime = System.currentTimeMillis()
        def endTime = startTime + 3600000L // 1 hour later

        when: "setting time values"
        outline.setStartTime(startTime)
        outline.setEndTime(endTime)

        then: "time values should be set correctly"
        outline.getStartTime() == startTime
        outline.getEndTime() == endTime
        outline.getEndTime() > outline.getStartTime()
    }

    def "test InstanceOutline toString"() {
        given: "an InstanceOutline with data"
        def outline = new InstanceOutline()
        outline.setId("instance123")
        outline.setWorkflowName("Test Workflow")

        when: "calling toString"
        def result = outline.toString()

        then: "toString should return a string"
        result != null
        result instanceof String
    }

    def "test InstanceOutline equals and hashCode"() {
        given: "two InstanceOutline instances"
        def outline1 = new InstanceOutline()
        def outline2 = new InstanceOutline()

        when: "setting same properties"
        outline1.setId("123")
        outline1.setWorkflowName("Test")
        outline2.setId("123")
        outline2.setWorkflowName("Test")

        then: "objects should have consistent equals and hashCode behavior"
        outline1.equals(outline1) // reflexive
        outline1.hashCode() == outline1.hashCode() // consistent
    }

    def "test InstanceOutline with special characters"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting values with special characters"
        outline.setId("instance-123_test@domain")
        outline.setWorkflowName("Test Workflow & Process")
        outline.setEntityId("entity#456\$test")

        then: "special characters should be handled correctly"
        outline.getId() == "instance-123_test@domain"
        outline.getWorkflowName() == "Test Workflow & Process"
        outline.getEntityId() == "entity#456\$test"
    }

    def "test InstanceOutline with long strings"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()
        def longString = "a" * 1000

        when: "setting long string values"
        outline.setId(longString)
        outline.setWorkflowName(longString)
        outline.setEntityId(longString)

        then: "long strings should be accepted"
        outline.getId() == longString
        outline.getWorkflowName() == longString
        outline.getEntityId() == longString
    }

    def "test InstanceOutline property chaining"() {
        when: "chaining property setters"
        def outline = new InstanceOutline()
        outline.setId("123")
        outline.setWorkflowName("Test")
        outline.setState(InstanceState.pass)

        then: "all properties should be set"
        outline.getId() == "123"
        outline.getWorkflowName() == "Test"
        outline.getState() == InstanceState.pass
    }

    def "test InstanceOutline default values"() {
        when: "creating new InstanceOutline"
        def outline = new InstanceOutline()

        then: "default values should be null"
        outline.getId() == null
        outline.getEntityId() == null
        outline.getObjectId() == null
        outline.getWorkflowId() == null
        outline.getWorkflowName() == null
        outline.getStartTime() == null
        outline.getEndTime() == null
        outline.getState() == null
        outline.getPools() == null
        outline.getMoreOperations() == null
    }

    def "test InstanceOutline with same start and end time"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()
        def sameTime = System.currentTimeMillis()

        when: "setting same start and end time"
        outline.setStartTime(sameTime)
        outline.setEndTime(sameTime)

        then: "times should be equal"
        outline.getStartTime() == outline.getEndTime()
    }

    def "test InstanceOutline with zero time values"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting zero time values"
        outline.setStartTime(0L)
        outline.setEndTime(0L)

        then: "zero values should be accepted"
        outline.getStartTime() == 0L
        outline.getEndTime() == 0L
    }

    def "test InstanceOutline with negative time values"() {
        given: "an InstanceOutline instance"
        def outline = new InstanceOutline()

        when: "setting negative time values"
        outline.setStartTime(-1L)
        outline.setEndTime(-1L)

        then: "negative values should be accepted"
        outline.getStartTime() == -1L
        outline.getEndTime() == -1L
    }
}
