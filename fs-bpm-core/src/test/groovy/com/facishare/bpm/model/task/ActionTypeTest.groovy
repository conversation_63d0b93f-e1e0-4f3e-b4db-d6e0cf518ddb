package com.facishare.bpm.model.task

import spock.lang.Specification

/**
 * Test for ActionType enum
 */
class ActionTypeTest extends Specification {

    def "test ActionType enum values"() {
        expect: "all enum values should be accessible"
        ActionType.person != null
        ActionType.overrun != null
    }

    def "test ActionType valueOf"() {
        expect: "valueOf should work for all enum values"
        ActionType.valueOf("person") == ActionType.person
        ActionType.valueOf("overrun") == ActionType.overrun
    }

    def "test ActionType values() method"() {
        when: "getting all enum values"
        def values = ActionType.values()

        then: "should contain all expected values"
        values.length == 2
        values.contains(ActionType.person)
        values.contains(ActionType.overrun)
    }

    def "test ActionType toString"() {
        expect: "toString should return the enum name"
        ActionType.person.toString() == "person"
        ActionType.overrun.toString() == "overrun"
    }

    def "test ActionType name"() {
        expect: "name() should return the enum name"
        ActionType.person.name() == "person"
        ActionType.overrun.name() == "overrun"
    }

    def "test ActionType ordinal"() {
        when: "getting ordinal values"
        def personOrdinal = ActionType.person.ordinal()
        def overrunOrdinal = ActionType.overrun.ordinal()

        then: "ordinals should be different"
        personOrdinal != overrunOrdinal
        personOrdinal >= 0
        overrunOrdinal >= 0
    }

    def "test ActionType equality"() {
        expect: "enum values should be equal to themselves"
        ActionType.person == ActionType.person
        ActionType.overrun == ActionType.overrun

        and: "different enum values should not be equal"
        ActionType.person != ActionType.overrun
    }

    def "test ActionType hashCode"() {
        when: "getting hash codes"
        def personHash = ActionType.person.hashCode()
        def overrunHash = ActionType.overrun.hashCode()

        then: "hash codes should be consistent"
        ActionType.person.hashCode() == personHash
        ActionType.overrun.hashCode() == overrunHash

        and: "different enum values should have different hash codes"
        personHash != overrunHash
    }

    def "test ActionType desc property"() {
        expect: "desc property should have correct values"
        ActionType.person.desc == "事件完成"
        ActionType.overrun.desc == "超时回调"
    }

    def "test ActionType engineParam property"() {
        expect: "engineParam property should have correct values"
        ActionType.person.engineParam == "person"
        ActionType.overrun.engineParam == "overrun"
    }

    def "test ActionType in switch statement"() {
        when: "using enum in switch statement"
        def result = getActionDescription(ActionType.person)

        then: "switch should work correctly"
        result == "Person action"

        when: "testing other enum values"
        def overrunResult = getActionDescription(ActionType.overrun)

        then: "all should work correctly"
        overrunResult == "Overrun action"
    }

    def "test ActionType in collections"() {
        when: "using enum in collections"
        def enumSet = [ActionType.person] as Set
        def enumList = [ActionType.overrun, ActionType.person]

        then: "collections should work correctly"
        enumSet.contains(ActionType.person)
        !enumSet.contains(ActionType.overrun)

        enumList.contains(ActionType.overrun)
        enumList.contains(ActionType.person)
    }

    def "test ActionType comparison"() {
        when: "comparing enum values"
        def comparison1 = ActionType.person.compareTo(ActionType.overrun)
        def comparison2 = ActionType.person.compareTo(ActionType.person)

        then: "comparisons should be consistent with ordinal values"
        comparison1 != 0 // different enum values
        comparison2 == 0 // same enum value
    }

    def "test ActionType serialization compatibility"() {
        expect: "enum should be serializable"
        ActionType.person instanceof Serializable
        ActionType.overrun instanceof Serializable
    }

    def "test ActionType properties are not null"() {
        expect: "all properties should be non-null"
        ActionType.person.desc != null
        ActionType.person.engineParam != null
        ActionType.overrun.desc != null
        ActionType.overrun.engineParam != null
    }

    def "test ActionType properties are not empty"() {
        expect: "all properties should be non-empty"
        !ActionType.person.desc.isEmpty()
        !ActionType.person.engineParam.isEmpty()
        !ActionType.overrun.desc.isEmpty()
        !ActionType.overrun.engineParam.isEmpty()
    }

    def "test ActionType desc and engineParam consistency"() {
        expect: "engineParam should match enum name"
        ActionType.person.engineParam == ActionType.person.name()
        ActionType.overrun.engineParam == ActionType.overrun.name()
    }

    def "test ActionType constructor behavior"() {
        expect: "enum values should have correct constructor parameters"
        ActionType.person.desc == "事件完成"
        ActionType.person.engineParam == "person"
        ActionType.overrun.desc == "超时回调"
        ActionType.overrun.engineParam == "overrun"
    }

    private String getActionDescription(ActionType type) {
        switch (type) {
            case ActionType.person:
                return "Person action"
            case ActionType.overrun:
                return "Overrun action"
            default:
                return "Unknown action type"
        }
    }
}
