package com.facishare.bpm.model

import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import spock.lang.Specification

/**
 * Test for UserTaskType
 */
class UserTaskTypeTest extends Specification {

    def "test UserTaskType creation with type and name"() {
        when: "creating UserTaskType with type and name"
        def userTaskType = new UserTaskType("Test Task", "testType")

        then: "properties should be set correctly"
        userTaskType.getName() == "Test Task"
        userTaskType.getType() == "testType"
        userTaskType.getSubType() == null
    }

    def "test UserTaskType creation with type, name and subType"() {
        when: "creating UserTaskType with type, name and subType"
        def userTaskType = new UserTaskType("Test Task", "testType", "testSubType")

        then: "properties should be set correctly"
        userTaskType.getName() == "Test Task"
        userTaskType.getType() == "testType"
        userTaskType.getSubType() == "testSubType"
    }

    def "test UserTaskType setters and getters"() {
        given: "a UserTaskType instance"
        def userTaskType = new UserTaskType("Initial", "initial")

        when: "setting properties"
        userTaskType.setName("Updated Task")
        userTaskType.setType("updatedType")
        userTaskType.setSubType("updatedSubType")

        then: "properties should be updated"
        userTaskType.getName() == "Updated Task"
        userTaskType.getType() == "updatedType"
        userTaskType.getSubType() == "updatedSubType"
    }

    def "test UserTaskType with null values"() {
        when: "creating UserTaskType with null values"
        def userTaskType = new UserTaskType(null, null)

        then: "null values should be accepted"
        userTaskType.getName() == null
        userTaskType.getType() == null
        userTaskType.getSubType() == null
    }

    def "test UserTaskType with empty strings"() {
        when: "creating UserTaskType with empty strings"
        def userTaskType = new UserTaskType("", "")

        then: "empty strings should be accepted"
        userTaskType.getName() == ""
        userTaskType.getType() == ""
        userTaskType.getSubType() == null
    }

    def "test UserTaskType toString"() {
        given: "a UserTaskType with data"
        def userTaskType = new UserTaskType("Test Task", "testType", "testSubType")

        when: "calling toString"
        def result = userTaskType.toString()

        then: "toString should return a string"
        result != null
        result instanceof String
    }

    def "test UserTaskType equals and hashCode"() {
        given: "two UserTaskType instances"
        def userTaskType1 = new UserTaskType("Test", "type")
        def userTaskType2 = new UserTaskType("Test", "type")

        expect: "objects should have consistent equals and hashCode behavior"
        userTaskType1.equals(userTaskType1) // reflexive
        userTaskType1.hashCode() == userTaskType1.hashCode() // consistent
    }

    def "test UserTaskType with special characters"() {
        when: "creating UserTaskType with special characters"
        def userTaskType = new UserTaskType("Test & Task", "type@domain", "sub#type")

        then: "special characters should be handled correctly"
        userTaskType.getName() == "Test & Task"
        userTaskType.getType() == "type@domain"
        userTaskType.getSubType() == "sub#type"
    }

    def "test UserTaskType with long strings"() {
        given: "long strings"
        def longString = "a" * 1000

        when: "creating UserTaskType with long strings"
        def userTaskType = new UserTaskType(longString, longString, longString)

        then: "long strings should be accepted"
        userTaskType.getName() == longString
        userTaskType.getType() == longString
        userTaskType.getSubType() == longString
    }

    def "test UserTaskType property chaining"() {
        when: "chaining property setters"
        def userTaskType = new UserTaskType("Initial", "initial")
        userTaskType.setName("Updated")
        userTaskType.setType("updated")
        userTaskType.setSubType("updatedSub")

        then: "all properties should be set"
        userTaskType.getName() == "Updated"
        userTaskType.getType() == "updated"
        userTaskType.getSubType() == "updatedSub"
    }

    def "test UserTaskType class structure"() {
        expect: "UserTaskType should be a proper class"
        UserTaskType.class != null
        UserTaskType.class.getDeclaredFields().length >= 3
    }

    def "test UserTaskType with different constructor combinations"() {
        when: "creating UserTaskType instances with different constructors"
        def type1 = new UserTaskType("Name1", "type1")
        def type2 = new UserTaskType("Name2", "type2", "subType2")

        then: "both should be created correctly"
        type1.getName() == "Name1"
        type1.getType() == "type1"
        type1.getSubType() == null

        type2.getName() == "Name2"
        type2.getType() == "type2"
        type2.getSubType() == "subType2"
    }

    def "test UserTaskType comparison with different instances"() {
        given: "different UserTaskType instances"
        def type1 = new UserTaskType("Same", "same")
        def type2 = new UserTaskType("Same", "same")
        def type3 = new UserTaskType("Different", "different")

        expect: "comparison behavior should be consistent"
        // Note: Lombok @Data generates equals based on field values, not object identity
        type1 == type2 // same field values
        type1 != type3 // different content
        type2 != type3 // different content
    }

    def "test UserTaskType with whitespace handling"() {
        when: "creating UserTaskType with whitespace"
        def userTaskType = new UserTaskType("  Test Task  ", "  testType  ", "  testSubType  ")

        then: "whitespace should be preserved"
        userTaskType.getName() == "  Test Task  "
        userTaskType.getType() == "  testType  "
        userTaskType.getSubType() == "  testSubType  "
    }
}
