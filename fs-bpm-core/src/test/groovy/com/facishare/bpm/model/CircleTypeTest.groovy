package com.facishare.bpm.model

import spock.lang.Specification

/**
 * Test for CircleType enum
 */
class CircleTypeTest extends Specification {

    def "test CircleType enum values"() {
        expect: "all enum values should be accessible"
        CircleType.ALL != null
        CircleType.CHILD != null
        CircleType.SELF != null
    }

    def "test CircleType valueOf"() {
        expect: "valueOf should work for all enum values"
        CircleType.valueOf("ALL") == CircleType.ALL
        CircleType.valueOf("CHILD") == CircleType.CHILD
        CircleType.valueOf("SELF") == CircleType.SELF
    }

    def "test CircleType values() method"() {
        when: "getting all enum values"
        def values = CircleType.values()

        then: "should contain all expected values"
        values.length == 3
        values.contains(CircleType.ALL)
        values.contains(CircleType.CHILD)
        values.contains(CircleType.SELF)
    }

    def "test CircleType toString"() {
        expect: "toString should return the enum name"
        CircleType.ALL.toString() == "ALL"
        CircleType.CHILD.toString() == "CHILD"
        CircleType.SELF.toString() == "SELF"
    }

    def "test CircleType name"() {
        expect: "name() should return the enum name"
        CircleType.ALL.name() == "ALL"
        CircleType.CHILD.name() == "CHILD"
        CircleType.SELF.name() == "SELF"
    }

    def "test CircleType ordinal"() {
        when: "getting ordinal values"
        def allOrdinal = CircleType.ALL.ordinal()
        def childOrdinal = CircleType.CHILD.ordinal()
        def selfOrdinal = CircleType.SELF.ordinal()

        then: "ordinals should be different and sequential"
        allOrdinal != childOrdinal
        childOrdinal != selfOrdinal
        allOrdinal >= 0
        childOrdinal >= 0
        selfOrdinal >= 0
    }

    def "test CircleType equality"() {
        expect: "enum values should be equal to themselves"
        CircleType.ALL == CircleType.ALL
        CircleType.CHILD == CircleType.CHILD
        CircleType.SELF == CircleType.SELF

        and: "different enum values should not be equal"
        CircleType.ALL != CircleType.CHILD
        CircleType.CHILD != CircleType.SELF
        CircleType.SELF != CircleType.ALL
    }

    def "test CircleType hashCode"() {
        when: "getting hash codes"
        def allHash = CircleType.ALL.hashCode()
        def childHash = CircleType.CHILD.hashCode()
        def selfHash = CircleType.SELF.hashCode()

        then: "hash codes should be consistent"
        CircleType.ALL.hashCode() == allHash
        CircleType.CHILD.hashCode() == childHash
        CircleType.SELF.hashCode() == selfHash

        and: "different enum values should have different hash codes"
        allHash != childHash
        childHash != selfHash
        selfHash != allHash
    }

    def "test CircleType in switch statement"() {
        when: "using enum in switch statement"
        def result = getCircleDescription(CircleType.ALL)

        then: "switch should work correctly"
        result == "All circles"

        when: "testing other enum values"
        def childResult = getCircleDescription(CircleType.CHILD)
        def selfResult = getCircleDescription(CircleType.SELF)

        then: "all should work correctly"
        childResult == "Child circles"
        selfResult == "Self circle"
    }

    def "test CircleType in collections"() {
        when: "using enum in collections"
        def enumSet = [CircleType.ALL, CircleType.CHILD] as Set
        def enumList = [CircleType.SELF, CircleType.ALL]

        then: "collections should work correctly"
        enumSet.contains(CircleType.ALL)
        enumSet.contains(CircleType.CHILD)
        !enumSet.contains(CircleType.SELF)

        enumList.contains(CircleType.SELF)
        enumList.contains(CircleType.ALL)
        !enumList.contains(CircleType.CHILD)
    }

    def "test CircleType comparison"() {
        when: "comparing enum values"
        def comparison1 = CircleType.ALL.compareTo(CircleType.CHILD)
        def comparison2 = CircleType.CHILD.compareTo(CircleType.SELF)
        def comparison3 = CircleType.ALL.compareTo(CircleType.ALL)

        then: "comparisons should be consistent with ordinal values"
        comparison1 < 0 // ALL comes before CHILD
        comparison2 < 0 // CHILD comes before SELF
        comparison3 == 0 // same enum value
    }

    def "test CircleType serialization compatibility"() {
        expect: "enum should be serializable"
        CircleType.ALL instanceof Serializable
        CircleType.CHILD instanceof Serializable
        CircleType.SELF instanceof Serializable
    }

    private String getCircleDescription(CircleType type) {
        switch (type) {
            case CircleType.ALL:
                return "All circles"
            case CircleType.CHILD:
                return "Child circles"
            case CircleType.SELF:
                return "Self circle"
            default:
                return "Unknown circle type"
        }
    }
}
