package com.facishare.bpm.model.paas.engine.bpm

import spock.lang.Specification

/**
 * Test for ExecutionTypeEnum
 */
class ExecutionTypeEnumTest extends Specification {

    def "test ExecutionTypeEnum values"() {
        expect: "all enum values should be accessible"
        ExecutionTypeEnum.approve != null
        ExecutionTypeEnum.update != null
        ExecutionTypeEnum.operation != null
        ExecutionTypeEnum.custom != null
        ExecutionTypeEnum.externalApplyTask != null
        ExecutionTypeEnum.addRelatedObject != null
    }

    def "test ExecutionTypeEnum valueOf"() {
        expect: "valueOf should work for all enum values"
        ExecutionTypeEnum.valueOf("approve") == ExecutionTypeEnum.approve
        ExecutionTypeEnum.valueOf("update") == ExecutionTypeEnum.update
        ExecutionTypeEnum.valueOf("operation") == ExecutionTypeEnum.operation
        ExecutionTypeEnum.valueOf("custom") == ExecutionTypeEnum.custom
        ExecutionTypeEnum.valueOf("externalApplyTask") == ExecutionTypeEnum.externalApplyTask
        ExecutionTypeEnum.valueOf("addRelatedObject") == ExecutionTypeEnum.addRelatedObject
    }

    def "test ExecutionTypeEnum values() method"() {
        when: "getting all enum values"
        def values = ExecutionTypeEnum.values()

        then: "should contain all expected values"
        values.length >= 6
        values.contains(ExecutionTypeEnum.approve)
        values.contains(ExecutionTypeEnum.update)
        values.contains(ExecutionTypeEnum.operation)
        values.contains(ExecutionTypeEnum.custom)
        values.contains(ExecutionTypeEnum.externalApplyTask)
        values.contains(ExecutionTypeEnum.addRelatedObject)
    }

    def "test ExecutionTypeEnum toString"() {
        expect: "toString should return the enum name"
        ExecutionTypeEnum.approve.toString() == "approve"
        ExecutionTypeEnum.update.toString() == "update"
        ExecutionTypeEnum.operation.toString() == "operation"
        ExecutionTypeEnum.custom.toString() == "custom"
        ExecutionTypeEnum.externalApplyTask.toString() == "externalApplyTask"
        ExecutionTypeEnum.addRelatedObject.toString() == "addRelatedObject"
    }

    def "test ExecutionTypeEnum name"() {
        expect: "name() should return the enum name"
        ExecutionTypeEnum.approve.name() == "approve"
        ExecutionTypeEnum.update.name() == "update"
        ExecutionTypeEnum.operation.name() == "operation"
        ExecutionTypeEnum.custom.name() == "custom"
        ExecutionTypeEnum.externalApplyTask.name() == "externalApplyTask"
        ExecutionTypeEnum.addRelatedObject.name() == "addRelatedObject"
    }

    def "test ExecutionTypeEnum ordinal"() {
        when: "getting ordinal values"
        def approveOrdinal = ExecutionTypeEnum.approve.ordinal()
        def updateOrdinal = ExecutionTypeEnum.update.ordinal()

        then: "ordinals should be different"
        approveOrdinal != updateOrdinal
        approveOrdinal >= 0
        updateOrdinal >= 0
    }

    def "test ExecutionTypeEnum equality"() {
        expect: "enum values should be equal to themselves"
        ExecutionTypeEnum.approve == ExecutionTypeEnum.approve
        ExecutionTypeEnum.update == ExecutionTypeEnum.update
        ExecutionTypeEnum.operation == ExecutionTypeEnum.operation
        ExecutionTypeEnum.custom == ExecutionTypeEnum.custom
        ExecutionTypeEnum.externalApplyTask == ExecutionTypeEnum.externalApplyTask
        ExecutionTypeEnum.addRelatedObject == ExecutionTypeEnum.addRelatedObject

        and: "different enum values should not be equal"
        ExecutionTypeEnum.approve != ExecutionTypeEnum.update
        ExecutionTypeEnum.update != ExecutionTypeEnum.operation
        ExecutionTypeEnum.operation != ExecutionTypeEnum.custom
        ExecutionTypeEnum.custom != ExecutionTypeEnum.externalApplyTask
        ExecutionTypeEnum.externalApplyTask != ExecutionTypeEnum.addRelatedObject
    }

    def "test ExecutionTypeEnum hashCode"() {
        when: "getting hash codes"
        def approveHash = ExecutionTypeEnum.approve.hashCode()
        def updateHash = ExecutionTypeEnum.update.hashCode()

        then: "hash codes should be consistent"
        ExecutionTypeEnum.approve.hashCode() == approveHash
        ExecutionTypeEnum.update.hashCode() == updateHash

        and: "different enum values should have different hash codes"
        approveHash != updateHash
    }

    def "test ExecutionTypeEnum in switch statement"() {
        when: "using enum in switch statement"
        def result = getTaskTypeDescription(ExecutionTypeEnum.approve)

        then: "switch should work correctly"
        result == "Approval Task"

        when: "testing other enum values"
        def updateResult = getTaskTypeDescription(ExecutionTypeEnum.update)
        def operationResult = getTaskTypeDescription(ExecutionTypeEnum.operation)

        then: "all should work correctly"
        updateResult == "Update Task"
        operationResult == "Operation Task"
    }

    def "test ExecutionTypeEnum in collections"() {
        when: "using enum in collections"
        def enumSet = [ExecutionTypeEnum.approve, ExecutionTypeEnum.update] as Set
        def enumList = [ExecutionTypeEnum.operation, ExecutionTypeEnum.custom]

        then: "collections should work correctly"
        enumSet.contains(ExecutionTypeEnum.approve)
        enumSet.contains(ExecutionTypeEnum.update)
        !enumSet.contains(ExecutionTypeEnum.operation)

        enumList.contains(ExecutionTypeEnum.operation)
        enumList.contains(ExecutionTypeEnum.custom)
        !enumList.contains(ExecutionTypeEnum.approve)
    }

    private String getTaskTypeDescription(ExecutionTypeEnum type) {
        switch (type) {
            case ExecutionTypeEnum.approve:
                return "Approval Task"
            case ExecutionTypeEnum.update:
                return "Update Task"
            case ExecutionTypeEnum.operation:
                return "Operation Task"
            case ExecutionTypeEnum.custom:
                return "Custom Task"
            case ExecutionTypeEnum.externalApplyTask:
                return "External Apply Task"
            case ExecutionTypeEnum.addRelatedObject:
                return "Add Related Object Task"
            default:
                return "Unknown Task"
        }
    }
}
