package com.facishare.bpm.model.paas.engine.bpm

import spock.lang.Specification

/**
 * Test for InstanceState enum
 */
class InstanceStateTest extends Specification {

    def "test InstanceState enum values"() {
        expect: "all enum values should be accessible"
        InstanceState.in_progress != null
        InstanceState.pass != null
        InstanceState.cancel != null
        InstanceState.error != null
        InstanceState.in_progress_or_error != null
        InstanceState.pass_or_cancel != null
    }

    def "test InstanceState valueOf"() {
        expect: "valueOf should work for all enum values"
        InstanceState.valueOf("in_progress") == InstanceState.in_progress
        InstanceState.valueOf("pass") == InstanceState.pass
        InstanceState.valueOf("cancel") == InstanceState.cancel
        InstanceState.valueOf("error") == InstanceState.error
        InstanceState.valueOf("in_progress_or_error") == InstanceState.in_progress_or_error
        InstanceState.valueOf("pass_or_cancel") == InstanceState.pass_or_cancel
    }

    def "test InstanceState values() method"() {
        when: "getting all enum values"
        def values = InstanceState.values()

        then: "should contain all expected values"
        values.length == 6
        values.contains(InstanceState.in_progress)
        values.contains(InstanceState.pass)
        values.contains(InstanceState.cancel)
        values.contains(InstanceState.error)
        values.contains(InstanceState.in_progress_or_error)
        values.contains(InstanceState.pass_or_cancel)
    }

    def "test InstanceState toString"() {
        expect: "toString should return the enum name"
        InstanceState.in_progress.toString() == "in_progress"
        InstanceState.pass.toString() == "pass"
        InstanceState.error.toString() == "error"
    }

    def "test InstanceState name"() {
        expect: "name() should return the enum name"
        InstanceState.in_progress.name() == "in_progress"
        InstanceState.pass.name() == "pass"
        InstanceState.error.name() == "error"
    }

    def "test InstanceState ordinal"() {
        when: "getting ordinal values"
        def inProgressOrdinal = InstanceState.in_progress.ordinal()
        def passOrdinal = InstanceState.pass.ordinal()
        def errorOrdinal = InstanceState.error.ordinal()

        then: "ordinals should be different and sequential"
        inProgressOrdinal != passOrdinal
        passOrdinal != errorOrdinal
        inProgressOrdinal >= 0
        passOrdinal >= 0
        errorOrdinal >= 0
    }

    def "test InstanceState equality"() {
        expect: "enum values should be equal to themselves"
        InstanceState.in_progress == InstanceState.in_progress
        InstanceState.pass == InstanceState.pass
        InstanceState.error == InstanceState.error

        and: "different enum values should not be equal"
        InstanceState.in_progress != InstanceState.pass
        InstanceState.pass != InstanceState.error
        InstanceState.error != InstanceState.in_progress
    }

    def "test InstanceState hashCode"() {
        when: "getting hash codes"
        def inProgressHash = InstanceState.in_progress.hashCode()
        def passHash = InstanceState.pass.hashCode()
        def errorHash = InstanceState.error.hashCode()

        then: "hash codes should be consistent"
        InstanceState.in_progress.hashCode() == inProgressHash
        InstanceState.pass.hashCode() == passHash
        InstanceState.error.hashCode() == errorHash

        and: "different enum values should have different hash codes"
        inProgressHash != passHash
        passHash != errorHash
        errorHash != inProgressHash
    }

    def "test InstanceState in switch statement"() {
        when: "using enum in switch statement"
        def result = getStateDescription(InstanceState.in_progress)

        then: "switch should work correctly"
        result == "In Progress"

        when: "testing other enum values"
        def passResult = getStateDescription(InstanceState.pass)
        def errorResult = getStateDescription(InstanceState.error)

        then: "all should work correctly"
        passResult == "Passed"
        errorResult == "Error"
    }

    def "test InstanceState in collections"() {
        when: "using enum in collections"
        def enumSet = [InstanceState.in_progress, InstanceState.pass] as Set
        def enumList = [InstanceState.error, InstanceState.in_progress]

        then: "collections should work correctly"
        enumSet.contains(InstanceState.in_progress)
        enumSet.contains(InstanceState.pass)
        !enumSet.contains(InstanceState.error)

        enumList.contains(InstanceState.error)
        enumList.contains(InstanceState.in_progress)
        !enumList.contains(InstanceState.pass)
    }

    def "test InstanceState comparison"() {
        when: "comparing enum values"
        def comparison1 = InstanceState.in_progress.compareTo(InstanceState.pass)
        def comparison2 = InstanceState.pass.compareTo(InstanceState.error)
        def comparison3 = InstanceState.in_progress.compareTo(InstanceState.in_progress)

        then: "comparisons should be consistent with ordinal values"
        comparison1 != 0 // different enum values
        comparison2 != 0 // different enum values
        comparison3 == 0 // same enum value
    }

    def "test InstanceState serialization compatibility"() {
        expect: "enum should be serializable"
        InstanceState.in_progress instanceof Serializable
        InstanceState.pass instanceof Serializable
        InstanceState.error instanceof Serializable
    }

    def "test InstanceState with null comparison"() {
        expect: "enum values should not be equal to null"
        InstanceState.in_progress != null
        InstanceState.pass != null
        InstanceState.error != null
    }

    def "test InstanceState enum class properties"() {
        expect: "enum class should have expected properties"
        InstanceState.class.isEnum()
        InstanceState.class.getEnumConstants().length == 6
        InstanceState.class.getEnumConstants().contains(InstanceState.in_progress)
        InstanceState.class.getEnumConstants().contains(InstanceState.pass)
        InstanceState.class.getEnumConstants().contains(InstanceState.cancel)
        InstanceState.class.getEnumConstants().contains(InstanceState.error)
        InstanceState.class.getEnumConstants().contains(InstanceState.in_progress_or_error)
        InstanceState.class.getEnumConstants().contains(InstanceState.pass_or_cancel)
    }

    def "test InstanceState state transitions"() {
        when: "checking logical state transitions"
        def initialState = InstanceState.in_progress
        def successState = InstanceState.pass
        def failureState = InstanceState.error

        then: "states should represent logical workflow states"
        initialState != successState
        initialState != failureState
        successState != failureState
    }

    def "test InstanceState in maps"() {
        when: "using enum as map keys"
        def stateMap = [:]
        stateMap[InstanceState.in_progress] = "Running"
        stateMap[InstanceState.pass] = "Completed"
        stateMap[InstanceState.error] = "Failed"

        then: "enum should work as map keys"
        stateMap[InstanceState.in_progress] == "Running"
        stateMap[InstanceState.pass] == "Completed"
        stateMap[InstanceState.error] == "Failed"
        stateMap.size() == 3
    }

    def "test InstanceState enum constants immutability"() {
        when: "accessing enum constants multiple times"
        def state1 = InstanceState.in_progress
        def state2 = InstanceState.in_progress

        then: "should return the same instance"
        state1.is(state2)
        state1 == state2
    }

    private String getStateDescription(InstanceState state) {
        switch (state) {
            case InstanceState.in_progress:
                return "In Progress"
            case InstanceState.pass:
                return "Passed"
            case InstanceState.error:
                return "Error"
            default:
                return "Unknown state"
        }
    }
}
