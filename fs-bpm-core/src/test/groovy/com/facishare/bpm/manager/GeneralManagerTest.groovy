package com.facishare.bpm.manager

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.handler.task.button.TaskButtonHandler
import com.facishare.bpm.handler.task.complete.TaskCompleteHandler
import com.facishare.bpm.handler.task.data.TaskDataHandler
import com.facishare.bpm.handler.task.detail.GetTaskDetailHandler
import com.facishare.bpm.handler.task.detail.model.StandardData
import com.facishare.bpm.handler.task.form.UserFormToMetadataManager
import com.facishare.bpm.handler.task.form.model.FormAssembleType
import com.facishare.bpm.handler.task.lane.GetLaneTasksHandler
import com.facishare.bpm.manage.impl.*
import com.facishare.bpm.model.TaskParams
import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum
import com.facishare.bpm.model.paas.engine.bpm.Task
import com.facishare.bpm.model.task.LaneTask
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
import com.facishare.rest.core.model.RemoteContext
import com.github.jedis.support.MergeJedisCmd
import com.google.common.collect.Maps
import org.springframework.context.ApplicationContext
import spock.lang.Specification

class GeneralManagerTest extends Specification{
    def "test init method"() {
        given: "A mock application context and task button handlers"
        def applicationContext = Mock(ApplicationContext.class)
        def buttonCustomManager = new ButtonCustomManagerImpl()
        buttonCustomManager.applicationContext = applicationContext

        def handler1 = Mock(TaskButtonHandler.class)
        def handler2 = Mock(TaskButtonHandler.class)
        handler1.getTaskType() >> ExecutionTypeEnum.addRelatedObject
        handler2.getTaskType() >> ExecutionTypeEnum.approve

        when: "Initializing the button custom manager"
        buttonCustomManager.init()
        buttonCustomManager.getHandler(ExecutionTypeEnum.approve)

        then: "The handlers are retrieved from the application context and mapped correctly"
        1 * applicationContext.getBeansOfType(TaskButtonHandler) >> [(handler1): handler1, (handler2): handler2]
        buttonCustomManager.handlers.size() == 2
        buttonCustomManager.handlers[ExecutionTypeEnum.addRelatedObject] == handler1
        buttonCustomManager.handlers[ExecutionTypeEnum.approve] == handler2
    }

    def "DefinitionInitManagerImpl"() {
        given:
        DefinitionInitManagerImpl test = new DefinitionInitManagerImpl()
        test.init()
        def context = Mock(RemoteContext.class)
        context.getTenantId() >> "71557"
        context.getUserId() >> "1002"
        def bpmSimpleDefinitionDao = Mock(BpmSimpleDefinitionDao.class)
        test.outlineDao = bpmSimpleDefinitionDao
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        WorkflowOutline out = ["sourceWorkflowId" : "123"] as WorkflowOutline
        paasWorkflow.deploy(context, false, test.getInitDefinition("405678154806329344")) >> out
        test.paasWorkflow = paasWorkflow
        def workflowExtensionDao = Mock(DefinitionExtensionDao.class)
        test.workflowExtensionDao = workflowExtensionDao
        when:
        try {
            test.getInitDefinition("405678154806329344")
            test.getInitDefinition("111")
        }catch (Exception e){}
        test.loadTemplate("/aaa")
        def res = test.definitionInit(context, "405678154806329344")
        then:
        Objects.isNull(res)
    }

    def "GetLaneTasksManagerImpl"() {
        given:
        def applicationContext = Mock(ApplicationContext.class)
        def getLaneTasksManagerImpl = new GetLaneTasksManagerImpl()
        getLaneTasksManagerImpl.applicationContext = applicationContext

        def handler1 = Mock(GetLaneTasksHandler.class)
        def handler2 = Mock(GetLaneTasksHandler.class)
        handler1.getTaskType() >> ExecutionTypeEnum.addRelatedObject
        handler2.getTaskType() >> ExecutionTypeEnum.approve
        1 * applicationContext.getBeansOfType(GetLaneTasksHandler) >> [(handler1): handler1, (handler2): handler2]

        when:
        getLaneTasksManagerImpl.init()
        getLaneTasksManagerImpl.getHandler(ExecutionTypeEnum.approve)

        then: "The handlers are retrieved from the application context and mapped correctly"
        getLaneTasksManagerImpl.handlerMap.size() == 2
        getLaneTasksManagerImpl.handlerMap[ExecutionTypeEnum.addRelatedObject] == handler1
        getLaneTasksManagerImpl.handlerMap[ExecutionTypeEnum.approve] == handler2
    }

    def "GetTaskFormManagerImpl.getData"() {
        given:
        def applicationContext = Mock(ApplicationContext.class)
        def ref = Mock(RefServiceManager.class)
        def getTaskFormManagerImpl = new GetTaskFormManagerImpl()
        getTaskFormManagerImpl.applicationContext = applicationContext
        def handler1 = Mock(UserFormToMetadataManager.class)
        def handler2 = Mock(UserFormToMetadataManager.class)
        def handler3 = Mock(UserFormToMetadataManager.class)
        def handler4 = Mock(UserFormToMetadataManager.class)
        handler1.getTaskType() >> FormAssembleType.data
        handler2.getTaskType() >> FormAssembleType.describe
        handler3.getTaskType() >> FormAssembleType.layout
        handler4.getTaskType() >> FormAssembleType.describeExt
        1 * applicationContext.getBeansOfType(UserFormToMetadataManager) >> [(handler1): handler1, (handler2): handler2, (handler3): handler3, (handler4): handler4]
        List list = [[]] as List
        Map map = ['record_type': '1'] as Map
        Map map2= Maps.newHashMap();
        handler1.execute(ref, "","",list, new TaskParams()) >> map
        handler2.execute(ref, "","",list, new TaskParams()) >> map2
        handler4.execute(ref, "","",list, new TaskParams()) >> map2

        when:
        getTaskFormManagerImpl.init()
        getTaskFormManagerImpl.getData(ref, "","", extension, new TaskParams())

        then:
        1==1

        where:
        extension ||res
        ["" : []] as Map || null
        ["form" : [[]]] as Map || null
    }

    def "GetTaskFormManagerImpl.setForm"() {
        given:
        def ref = Mock(RefServiceManager.class)
        def applicationContext = Mock(ApplicationContext.class)
        def getTaskFormManagerImpl = new GetTaskFormManagerImpl()
        getTaskFormManagerImpl.applicationContext = applicationContext

        // Mock the handlers
        def handler1 = Mock(UserFormToMetadataManager.class)
        def handler2 = Mock(UserFormToMetadataManager.class)
        def handler3 = Mock(UserFormToMetadataManager.class)
        def handler4 = Mock(UserFormToMetadataManager.class)
        handler1.getTaskType() >> FormAssembleType.data
        handler2.getTaskType() >> FormAssembleType.describe
        handler3.getTaskType() >> FormAssembleType.layout
        handler4.getTaskType() >> FormAssembleType.describeExt

        // Mock the handler execute methods to return valid data
        handler1.execute(_, _, _, _, _) >> ['record_type': '1']
        handler2.execute(_, _, _, _, _) >> [:]
        handler3.execute(_, _, _, _, _) >> [:]
        handler4.execute(_, _, _, _, _) >> [:]

        applicationContext.getBeansOfType(UserFormToMetadataManager) >> [(handler1): handler1, (handler2): handler2, (handler3): handler3, (handler4): handler4]

        Task task = new Task()
        Map bpmExtension = ['layoutType':layoutType]
        task.bpmExtension = bpmExtension
        TaskParams params = new TaskParams()
        params.notGetDatas = notGetDatas
        Map extension = ['form': [[]]]

        when:
        getTaskFormManagerImpl.init()
        getTaskFormManagerImpl.setForm(ref, task, params,new LaneTask(),null,null, new StandardData(),extension)

        then:
        1==1

        where:
        layoutType | notGetDatas
        "objectFlowLayout" | null
        "defaultLayout" | true
        "defaultLayout" | false
    }
    def "GetTaskManagerImpl"() {
        given:
        def applicationContext = Mock(ApplicationContext.class)
        def getTaskManagerImpl = new GetTaskManagerImpl()
        getTaskManagerImpl.applicationContext = applicationContext

        def handler1 = Mock(GetTaskDetailHandler.class)
        def handler2 = Mock(GetTaskDetailHandler.class)
        handler1.getTaskType() >> ExecutionTypeEnum.addRelatedObject
        handler2.getTaskType() >> ExecutionTypeEnum.approve
        1 * applicationContext.getBeansOfType(GetTaskDetailHandler) >> [(handler1): handler1, (handler2): handler2]

        when:
        getTaskManagerImpl.init()
        getTaskManagerImpl.getHandler(ExecutionTypeEnum.approve)

        then: "The handlers are retrieved from the application context and mapped correctly"
        getTaskManagerImpl.handlers.size() == 2
        getTaskManagerImpl.handlers[ExecutionTypeEnum.addRelatedObject] == handler1
        getTaskManagerImpl.handlers[ExecutionTypeEnum.approve] == handler2
    }

    def "RedisManagerImpl"() {
        given:

        when:
        RedisManagerImpl test = new RedisManagerImpl()
        MergeJedisCmd cmd = Mock(MergeJedisCmd.class)
        test.redisClient = cmd
        cmd.set("a", "a", "NX", "EX", 15) >> "OK"
        cmd.set("a", "a", "NX", "PX", 3) >> "OK"
        cmd.set("a", "a", "NX", "EX", 4) >> "OK"
        cmd.hgetAll("a") >> ["A":"A"]
        cmd.hmset("a",["A":"A"]) >> "OK"
        cmd.del("a") >> 1L
        cmd.incrBy("a", 1L) >> 1L

        then:
        test.setValueWithExpire("a","a") == true
        test.setValueWithExpire("a","a", 3) == true
        test.setValueWithExpireTime("a","a", 4) == true
        Objects.nonNull(test.getValueMapByKey("a"))
        test.setValueOfMap("a",["A":"A"]) == true
        test.delete("") == 0L
        test.delete("a") == 1L
        test.incrBy("a", 1L) == 1L
    }

    def "TaskDataManagerImpl"() {
        given:
        def applicationContext = Mock(ApplicationContext.class)
        def taskDataManagerImpl = new TaskDataManagerImpl()
        taskDataManagerImpl.applicationContext = applicationContext

        def handler1 = Mock(TaskDataHandler.class)
        def handler2 = Mock(TaskDataHandler.class)
        handler1.getTaskType() >> ExecutionTypeEnum.addRelatedObject
        handler2.getTaskType() >> ExecutionTypeEnum.approve
        1 * applicationContext.getBeansOfType(TaskDataHandler) >> [(handler1): handler1, (handler2): handler2]

        when:
        taskDataManagerImpl.init()
        taskDataManagerImpl.getHandler(ExecutionTypeEnum.approve)

        then: "The handlers are retrieved from the application context and mapped correctly"
        taskDataManagerImpl.handlers.size() == 2
        taskDataManagerImpl.handlers[ExecutionTypeEnum.addRelatedObject] == handler1
        taskDataManagerImpl.handlers[ExecutionTypeEnum.approve] == handler2
    }

    def "TaskExecutionManagerImpl"() {
        given:
        def applicationContext = Mock(ApplicationContext.class)
        def taskExecutionManagerImpl = new TaskExecutionManagerImpl()
        taskExecutionManagerImpl.applicationContext = applicationContext

        def handler1 = Mock(TaskCompleteHandler.class)
        def handler2 = Mock(TaskCompleteHandler.class)
        handler1.getTaskType() >> ExecutionTypeEnum.addRelatedObject
        handler2.getTaskType() >> ExecutionTypeEnum.approve
        1 * applicationContext.getBeansOfType(TaskCompleteHandler) >> [(handler1): handler1, (handler2): handler2]

        when:
        taskExecutionManagerImpl.init()
        taskExecutionManagerImpl.getHandler(ExecutionTypeEnum.approve)

        then: "The handlers are retrieved from the application context and mapped correctly"
        taskExecutionManagerImpl.handlers.size() == 2
        taskExecutionManagerImpl.handlers[ExecutionTypeEnum.addRelatedObject] == handler1
        taskExecutionManagerImpl.handlers[ExecutionTypeEnum.approve] == handler2
    }

}
