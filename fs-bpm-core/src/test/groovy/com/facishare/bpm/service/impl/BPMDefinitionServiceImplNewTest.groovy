package com.facishare.bpm.service.impl

import com.facishare.bpm.RefServiceManager
import com.facishare.bpm.bpmn.ActivityExt
import com.facishare.bpm.bpmn.ExecutableWorkflowExt
import com.facishare.bpm.bpmn.ParallelGatewayExt
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException
import com.facishare.bpm.manage.DefineGenerateManager
import com.facishare.bpm.manage.RedisManager
import com.facishare.bpm.model.WorkflowOutline
import com.facishare.bpm.model.paas.engine.bpm.GetWorkflow
import com.facishare.bpm.model.paas.engine.bpm.WorkflowInstance
import com.facishare.bpm.model.paas.engine.bpm.WorkflowLogs
import com.facishare.bpm.model.resource.managegroup.GetManageGroupConfig
import com.facishare.bpm.model.resource.paas.PageResult
import com.facishare.bpm.model.resource.paas.org.GetUserInfoByUserIds
import com.facishare.bpm.producer.OpenAPIProducerManager
import com.facishare.bpm.proxy.ManageGroupProxy
import com.facishare.bpm.proxy.OrganizationServiceProxy
import com.facishare.bpm.remote.wf.PaasWorkflowServiceProxy
import com.facishare.bpm.service.BPMWorkflowDraftService
import com.facishare.flow.mongo.bizdb.BpmSimpleDefinitionDao
import com.facishare.flow.mongo.bizdb.DefinitionExtensionDao
import com.facishare.flow.mongo.bizdb.TenantDao
import com.facishare.flow.mongo.bizdb.entity.WorkflowOutlineEntity
import com.facishare.rest.core.model.RemoteContext
import com.facishare.rest.core.util.JacksonUtil
import com.google.common.collect.Sets
import org.apache.commons.io.IOUtils
import spock.lang.Specification

class BPMDefinitionServiceImplNewTest extends Specification {
    def deployWorkflow() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def remoteContext = Mock(RemoteContext.class)
        def bpmTenantService = Mock(BPMTenantServiceImpl.class)
        def redisManager = Mock(RedisManager.class)
        def defineGenerateManager = Mock(DefineGenerateManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def workflowExtensionDao = Mock(DefinitionExtensionDao.class)
        def bpmWorkflowDraftService = Mock(BPMWorkflowDraftService.class)
        remoteContext.getUserId() >> '71557'
        serviceManager.getContext() >> remoteContext
        bpmTenantService.hasQuota(serviceManager) >> true
        BPMDefinitionServiceImpl test = new BPMDefinitionServiceImpl()
        test.bpmTenantService = bpmTenantService
        test.redisManager = redisManager
        test.defineGenerateManager = defineGenerateManager
        test.outlineDao = outlineDao
        test.paasWorkflow = paasWorkflow
        test.workflowExtensionDao =  workflowExtensionDao
        test.bpmWorkflowDraftService = bpmWorkflowDraftService
        serviceManager.getDescribe("object_7prLH__c") >> ["a":'a']
        WorkflowOutlineEntity t = ['id':'111'] as WorkflowOutlineEntity
        outlineDao.createOrUpdate(_,_) >> t
        redisManager.setValueWithExpire("96cca34aa2139648db772816b6c3adaa", null) >>redisflag
        String path = BPMDefinitionServiceImplNewTest.class.getResource("/mockJson").getPath()
        File[] files = new File(path).listFiles();
        String templateStr = IOUtils.toString(files[0].toURI())
        def workflowOutline = JacksonUtil.fromJson(templateStr, WorkflowOutline.class)
        when:
        test.deployWorkflow(serviceManager, workflowOutline,true, true)
        test.updateWorkflow(serviceManager, workflowOutline, true, false)

        then:
        1==1
        where:
        redisflag || res
        true ||null
    }

    def checkControlled() {
        given:
        when:
        new BPMDefinitionServiceImpl().checkControlled("controlled")
        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def getAvailableWorkflows() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        serviceManager.isAdmin() >> isAdmin
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        List<WorkflowOutlineEntity> list= [['singleInstanceFlow': 1, "sourceWorkflowId" : "123456"] as WorkflowOutlineEntity]
        outlineDao.findByEntryType(_,_,_,_,_,_,_,_,_) >> list
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def result = new PageResult();
        result.setTotal(1)
        result.setDataList([["sourceWorkflowId":"123456"] as WorkflowInstance])
        paasWorkflow.getWorkflowInstances(_,_,_,_,_,_,_,_,_) >> result
        def test = new BPMDefinitionServiceImpl()
        test.outlineDao = outlineDao
        test.paasWorkflow = paasWorkflow
        serviceManager.getDeptIdsByUserId() >> circleIds
        serviceManager.getUserId() >> "1002"

        when:
        test.getAvailableWorkflows(serviceManager, "", "AA", true, 0, true,true)
        then:
        1==1
        where:
        isAdmin | circleIds
        true | null
        false | []
        false | null
    }

    def getWorkflowOutlines() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def manageGroupProxy = Mock(ManageGroupProxy.class)
        def bpmWorkflowDraftService = Mock(BPMWorkflowDraftService.class)
        GetManageGroupConfig.Result manageGroupConfig = new GetManageGroupConfig.Result()
        manageGroupConfig.result = ["apiNames" : apiNames] as GetManageGroupConfig.ManageGroupResult
        manageGroupProxy.getManageGroupConfig(_,_) >> manageGroupConfig
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        PageResult<WorkflowOutlineEntity> outlinePageResult = new PageResult()
        outlinePageResult.dataList = [["id":"123"] as WorkflowOutlineEntity]
        outlineDao.find(_,_,_,_,_) >> outlinePageResult


        def test = new BPMDefinitionServiceImpl()
        test.manageGroupProxy = manageGroupProxy
        test.outlineDao =outlineDao
        test.bpmWorkflowDraftService = bpmWorkflowDraftService


        when:
        test.getWorkflowOutlines(serviceManager, null, null, null)
        then:
        1==1
        where:
        apiNames | res
        []|null
    }

    def deleteWorkflowById() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def openAPIProducerManager = Mock(OpenAPIProducerManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def bpmWorkflowDraftService = Mock(BPMWorkflowDraftService.class)
        WorkflowOutlineEntity e = new WorkflowOutlineEntity()
        e.controlStatus = "111"
        e.isDeleted = isDeleted
        outlineDao.find(_,_) >> e
        outlineDao.delete(_,_,_) >> e
        def test = new BPMDefinitionServiceImpl()
        test.outlineDao = outlineDao
        test.openAPIProducerManager = openAPIProducerManager
        test.paasWorkflow = paasWorkflow
        test.bpmWorkflowDraftService = bpmWorkflowDraftService


        when:
        try{
            test.deleteWorkflowById(serviceManager, "123")
        }catch(Exception a){}

        then:
        1==1
        where:
        isDeleted || res
        true || null
        false || null
    }

    def getWorkflowOutlineById() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        outlineDao.find(_,_) >> []
        GetWorkflow.Result res = ["result":[]] as GetWorkflow.Result
        paasWorkflow.getWorkflowAndRule(_,_) >> res
        def test = new BPMDefinitionServiceImpl()

        test.outlineDao = outlineDao
        test.paasWorkflow = paasWorkflow
        when:
        try{
            test.getWorkflowOutlineById(serviceManager, "123")
        }catch(Exception a){}

        then:
        1==1
    }

    def getSourceWorkflowIdByOutlineId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        outlineDao.find(_,_) >> c
        def test = new BPMDefinitionServiceImpl()
        test.outlineDao = outlineDao
        when:
        try{
            test.getSourceWorkflowIdByOutlineId(serviceManager, "123")
        }catch(Exception a){}

        then:
        1==1
        where:
        c || res
        null || null
        ["sourceWorkflowId" : "123"] as WorkflowOutlineEntity || null
    }

    def getWorkflowOutlineByIdOfClearRule() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        outlineDao.find(_,_) >> []
        GetWorkflow.Result res = ["result":["ruleJson":"{\"ruleId\":\"123\",\"deleted\":false}","workflowJson":"{}"] as GetWorkflow.Detail] as GetWorkflow.Result
        paasWorkflow.getWorkflowAndRule(_,_) >> res
        def test = new BPMDefinitionServiceImpl()
        test.outlineDao = outlineDao
        test.paasWorkflow = paasWorkflow
        when:
        test.getWorkflowOutlineByIdOfClearRule(serviceManager, "123")
        then:
        thrown(BPMWorkflowDefVerifyException)
    }

    def enableWorkflow() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def openAPIProducerManager = Mock(OpenAPIProducerManager.class)
        def organizationServiceProxy = Mock(OrganizationServiceProxy.class)
        outlineDao.find(_,_) >> []
        outlineDao.enable(_,_,_,_) >> ["sourceWorkflowId" : "123"]
        paasWorkflow.updateDefinitionStatus(_,_,_) >> null
        def test = new BPMDefinitionServiceImpl()
        test.outlineDao = outlineDao
        test.paasWorkflow = paasWorkflow
        test.openAPIProducerManager = openAPIProducerManager
        test.organizationServiceProxy = organizationServiceProxy
        when:
        def result = test.enableWorkflow(serviceManager, "123", true)
        then:
        result == true
    }

    def getWorkflowExtensionByWorkflowId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        RemoteContext remoteContext = Mock(RemoteContext.class)
        def workflowExtensionDao = Mock(DefinitionExtensionDao.class)
        def test = new BPMDefinitionServiceImpl()
        test.workflowExtensionDao=workflowExtensionDao
        when:
        test.getWorkflowExtensionByWorkflowId(serviceManager, "123")
        test.getWorkflowExtensionByWorkflowId(remoteContext, "123")
        then:
        1==1
    }

    def getWorkflowOutlineBySourceWorkflowId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        outlineDao.findBySourceWorkflowId(_,_) >> res
        outlineDao.findSimpleOutlineBySourceWorkflowIds(_,_) >> null
        outlineDao.getWorkflowEntryTypeNameMap(_,_) >> null
        GetWorkflow.Result defRes = ["result":["ruleJson":"{\"ruleId\":\"123\",\"deleted\":false}"] as GetWorkflow.Detail] as GetWorkflow.Result
        paasWorkflow.getWorkflowAndRule(_,_) >> defRes
        paasWorkflow.getWorkflow(_,_) >> null
        def test = new BPMDefinitionServiceImpl()
        test.outlineDao=outlineDao
        test.paasWorkflow = paasWorkflow
        when:
        try {
            test.getWorkflowById(serviceManager, "1111")
            test.getWorkflowEntryTypeNameMap(serviceManager, sourceIds)
            test.getWorkflowOutlineBySourceId(serviceManager, Sets.newHashSet())
            test.getWorkflowOutlineBySourceWorkflowId(serviceManager, "123")
            test.getWorkflowOutlineBySourceId(serviceManager, "123")
        }catch(Exception e){}
        then:
        1==1
        where:
        res || sourceIds
        null || null
        ["id" :"12"] as WorkflowOutlineEntity || ["1","2"]
    }

    def getActivityDefByActivityId() {
        given:
        def serviceManager = Mock(RefServiceManager.class)
        def paasWorkflow = Mock(PaasWorkflowServiceProxy.class)
        def outlineDao = Mock(BpmSimpleDefinitionDao.class)
        def tenantDao = Mock(TenantDao.class)
        def context = Mock(RemoteContext.class)
        def organizationServiceProxy = Mock(OrganizationServiceProxy.class)
        def ext = ["activities" : resultt] as ExecutableWorkflowExt
        paasWorkflow.getWorkflow(_,_) >> ext
        outlineDao.findAll(_) >> null
        paasWorkflow.getWorkflowLogs(_,_,_) >> new  WorkflowLogs.Result()
        outlineDao.getWorkflowOutlineBySourceIdAndOutlineIdWithDeleted(_,_,_) >> null
        organizationServiceProxy.getUserInfoByUserIds(_,_) >> [["name" : "123"]as GetUserInfoByUserIds.EmployeeInfo]
        outlineDao.find(_,_) >> null
        outlineDao.findBySourceWorkflowId(_,_) >> res
        def test = new BPMDefinitionServiceImpl()
        test.paasWorkflow = paasWorkflow
        test.outlineDao = outlineDao
        test.tenantDao =tenantDao
        test.organizationServiceProxy = organizationServiceProxy
        when:
        try {
            test.destroyTenant(serviceManager, true)
            test.getActivityDefByActivityId(serviceManager,"111","222")
            test.getAllWorkflowOutlines(serviceManager)
            test.getWorkflowLogs(serviceManager, null, null)
            test.getWorkflowOutlineBySourceIdAndOutlineId(serviceManager, null, null)
            test.assertDefinitionNotFound(null,null)
            test.assertDefinitionNotFoundBySourceWorkflowId(null, null)
        }catch(Exception e){}
        then:
        1==1
        where:
        resultt | res
        [["id" : "1"]as ActivityExt] | null
        [["id" : "222"]as ParallelGatewayExt] | ["id" : "112"] as WorkflowOutlineEntity
    }

}
