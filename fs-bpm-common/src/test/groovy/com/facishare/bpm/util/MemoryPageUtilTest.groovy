package com.facishare.bpm.util

import com.facishare.bpm.util.memory.page.MemoryPageUtil
import com.facishare.bpm.utils.helper.StopWatch
import com.google.common.collect.Lists
import lombok.Data
import spock.lang.Specification

/**
 * Created by <PERSON> on 20/04/2017.
 */
class MemoryPageUtilTest extends Specification {
    List<Person> persons = Lists.newArrayList()

    def 'runTimes' () {

        for (int i = 0; i < 100; i++){
            persons.add(new Person(i + "天天", System.currentTimeMillis()))
        }

        StopWatch s = StopWatch.create("test")
        int pageSize = 10
        int page = 0
        def result=MemoryPageUtil.getPageResult(persons, null, null, pageSize, page)
        s.lap("end")
        s.log()
        expect:
         result.dataList.size()==pageSize
    }

    @Data
    class Person {
        private String name
        private long age

        Person(String name,long age) {
            this.name=name;
            this.age=age;
        }
    }
}
