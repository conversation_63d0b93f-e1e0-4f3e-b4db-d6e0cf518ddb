package com.facishare.bpm.util.verifiy.handler;

import com.facishare.bpm.RefServiceManager;
import com.facishare.bpm.bpmn.ActivityExt;
import com.facishare.bpm.bpmn.UserTaskExt;
import com.facishare.bpm.exception.BPMBusinessExceptionCode;
import com.facishare.bpm.exception.BPMWorkflowDefVerifyException;
import com.facishare.bpm.model.paas.engine.bpm.BPMConstants;
import com.facishare.bpm.model.paas.engine.bpm.ExecutionTypeEnum;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey;
import com.facishare.bpm.model.paas.engine.bpm.WorkflowKey.ActivityKey.ExtensionKey;
import com.facishare.bpm.util.BPMExtensionUtils;
import com.facishare.bpm.util.verifiy.ValidateHandler;
import com.facishare.bpm.util.verifiy.Workflow;
import com.facishare.bpm.utils.model.Pair;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 1. 校验extension常量字段
 * entityId,entityName,executionType,objectId
 * <p>
 * 2. validate the node variables in the process of the statement
 * <p>
 * 3. validate the activity's ref objectId is create in pre activity
 * <p>
 *
 * <AUTHOR>
 * @date 17/3/15
 */
@Slf4j
public class ExtensionHandler implements ValidateHandler {
    @Override
    public void validate(Workflow workflow) {

        validateActivityObjectId(workflow);

        validateExpressionInVariable(workflow);

        validateLayoutTypeInfo(workflow);
    }

    private void validateExpressionInVariable(Workflow workflow) {
        Collection activitiesExpressionList = workflow.getActivitiesExpression();
        List variablesList = workflow.getVariablesList();
        boolean flag = variablesList.containsAll(activitiesExpressionList);
        if (!flag) {
            log.error("定义中表达式未添加到变量定义中,请检查variables中主对象表达式");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_EXPRESSION_NOT_FOUND_VARIABLE);
        }
    }

    private void validateActivityObjectId(Workflow workflow) {
        workflow.getUserTasks().forEach(userTask -> validateExtension(workflow, userTask));
        workflow.getLatencyTaskExt().forEach(latencyTaskExt -> validateExtension(workflow, latencyTaskExt));
    }

    private void validateExtension(Workflow workflow,UserTaskExt userTask){
        Map bpmExtension = (Map) userTask.getProperty(ExtensionKey.bpmExtension);
        String entityId = (String) bpmExtension.get(ExtensionKey.entityId);
        String entityName = (String) bpmExtension.get(ExtensionKey.entityName);
        String executionType = (String) bpmExtension.get(ExtensionKey.executionType);
        String relatedEntityId = (String) bpmExtension.get(ExtensionKey.relatedEntityId);
        String taskName = userTask.getName();

        if (StringUtils.isEmpty(entityId)) {
            log.info("[" + taskName + "] 未设置entityId");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_SET_BELONG_OBJECT,taskName);
        } else if (StringUtils.isEmpty(entityName)) {
            log.info("[" + taskName + "] 未设置entityName");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_SET_BELONG_OBJECT_NAME,taskName);
        } else if (StringUtils.isEmpty(executionType)) {
            log.info("[" + taskName + "] 未设置executionType");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_CONFIG_EXECUTIONTYPE,taskName);
        } else if (executionType.equals(ExecutionTypeEnum.addRelatedObject.name()) && Strings.isNullOrEmpty
                (relatedEntityId)) {
            log.info("[" + taskName + "] 未设置relatedEntityId");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_CONFIG_RELATEDENTITYID,taskName);
        }else if(executionType.equals(ExecutionTypeEnum.updateLookup.name())){
            validateLookupField(workflow.getServiceManager(), userTask);
        }
        //TODO 需要判断extension中的entityId是否是当前节点的entityId

        Object objectId = bpmExtension.get(ExtensionKey.objectId);
        if (null == objectId) {
            log.error("[" + taskName + "] 未设置objectId");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VALIDATE_NOT_SET_BUSINESS_RECORD,taskName);
        }
        if (objectId instanceof Map) {
            //{0} 节点数据选择错误
            validateActivityObjectId(workflow, userTask, BPMExtensionUtils.transferExpression(objectId));
        } else {
            log.error("[" + taskName + "] 请检查 objectId 扩展表达式");
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_TASK_CHECK_OBJECT_INFO,taskName);
        }
    }

    private static void validateActivityObjectId(Workflow workflow, ActivityExt activityExt, BPMExtensionUtils.Expression objExpression) {
        //objectId更新的时候为表达式,新增的时候为空
        String id = activityExt.getId();

        Map<String, List<ActivityExt>> preActivitiesMap = workflow.getActivityPreNodeMap();
        //获取所有的userTask   activityId:UserTaskExt
        Map<String, UserTaskExt> activityMap = workflow.getUserTasks().stream().collect(Collectors.toMap(UserTaskExt::
                getId, userTask -> userTask));

        //activityId_3278423742##object_di9d__c
        String expr = objExpression.getInstanceVariableKey();
        //储存所有的activity上对面的表达式
        workflow.getActivitiesExpression().add(expr);

        //获取所有前置节点
        List<String> preActivityIds = preActivitiesMap.get(id).stream().map(ActivityExt::getId)
                .collect(Collectors.toList());

        //3278423742
        String objectRefPreActivityId = objExpression.getActivityId();
        //object_di9d__c
        String objectRefPreActivityEntityId = objExpression.getDescApiName();

        if (!"0".equals(objectRefPreActivityId)) {
            if (!preActivityIds.contains(objectRefPreActivityId)) {
                log.error("前置节点不在范围内:EI={}, WORKFLOW_ID = {},  TASK_NAME = {}, OBJECT_ID={}",
                        workflow.getExecutableWorkflow().getProperty("tenantId"),
                        workflow.getExecutableWorkflow().getId(),
                        activityExt.getName(),
                        objExpression.toString());
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NODE_DATA_ERROR,activityExt.getName());
            }

            UserTaskExt preActivity = activityMap.get(objectRefPreActivityId);
            Map preActivityExtension = (Map) preActivity.getProperty(ExtensionKey.bpmExtension);
            if (!objectRefPreActivityEntityId.equals(preActivityExtension.get(ExtensionKey.entityId))) {
                if ((!ExecutionTypeEnum.isMDObjectOrRelated((String) preActivityExtension.get(ExtensionKey.executionType)) && !Boolean.TRUE.equals(preActivity.getImportObject()))
                        || !objectRefPreActivityEntityId.equals(preActivityExtension.get(ExtensionKey.relatedEntityId))) {
                    throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_NODE_DATA_ERROR,activityExt.getName());
                }
            }

        } else {
            if (!workflow.getEntryType().equals(objExpression.getDescApiName())) {
                log.info("当前节点activityId=0,但entityId与入口对象不一致:entityId:{},entryType:{}", objExpression.getDescApiName(), workflow.getEntryType());
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_TASK_MISMATCH);
            }
        }

    }

    /**
     * 校验节点的布局类型 如果是流程对象布局 校验布局信息
     * @param workflow
     */
    private void validateLayoutTypeInfo(Workflow workflow){
        // key对象ApiName value 对应的流程对象布局ApiName
        Map<String, Set<Pair<String, String>>> entityLayoutMap = Maps.newHashMap();
        //找出节点中所有包含流程对象布局的layoutApiName
        for (UserTaskExt userTask : workflow.getUserTasks()) {
            Map bpmExtension = (Map) userTask.getProperty(ExtensionKey.bpmExtension);
            if(!BPMConstants.LayoutType.objectFlowLayout.equals(bpmExtension.get(ExtensionKey.layout_type))){
                continue;
            }
            //是流程对象布局 校验是否有ApiName Name 以及布局是否可用
            String layoutApiName = (String)bpmExtension.get(ExtensionKey.layout_api_name);
            String entityId = (String) bpmExtension.get(ExtensionKey.entityId);
            //如果是编辑关联字段的对象entityId是从字段上取的
            if(ExecutionTypeEnum.updateLookup.equals(userTask.getExtensionType())){
                entityId = (String) ((Map)workflow.getServiceManager().getFields(entityId).get(userTask.getFieldApiName())).get(BPMConstants.MetadataKey.targetApiName);
            }
            if(StringUtils.isBlank(layoutApiName) || StringUtils.isBlank(entityId)){
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_FLOW_LAYOUT_ERROR);
            }
            entityLayoutMap.computeIfAbsent(entityId, k->Sets.newHashSet()).add(new Pair<>(layoutApiName, userTask.getName()));
        }
        //没有流程对象布局信息 直接返回
        if(MapUtils.isEmpty(entityLayoutMap)){
            return;
        }
        for(String entityId : entityLayoutMap.keySet()){
            Set<String> noExist = workflow.getServiceManager().flowLayoutIsNoExist(entityId, entityLayoutMap.get(entityId).stream().map(temp -> temp.getKey()).collect(Collectors.toSet()));
            if(CollectionUtils.isNotEmpty(noExist)){
                String disabledLayoutTaskNameStr = entityLayoutMap.get(entityId).stream().filter(temp -> noExist.contains(temp.getKey())).map(temp -> temp.getValue()).collect(Collectors.joining(","));
                throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_FLOW_LAYOUT_DELETED, disabledLayoutTaskNameStr);
            }
        }
    }

    /**
     * 校验对象里关联的字段是否存在
     */
    private void validateLookupField(RefServiceManager serviceManager, UserTaskExt userTask){
        //获取所有是编辑关联字段对象的节点
        String entityId = userTask.getEntityId();
        String fieldApiName = userTask.getFieldApiName();
        if (StringUtils.isBlank(fieldApiName)){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_RELATION_FIELD, userTask.getName());
        }
        Map<String, Object> entityFieldMap = serviceManager.getFields(entityId);
        if (!entityFieldMap.containsKey(fieldApiName) || Boolean.FALSE.equals(((Map)entityFieldMap.get(fieldApiName)).get(BPMConstants.MetadataKey.isActive))){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_FLOW_RELATION_FIELD_DELETED, userTask.getName());
        }
        if(!BPMConstants.MetadataKey.objectReference.equals(((Map)entityFieldMap.get(fieldApiName)).get(BPMConstants.MetadataKey.type))){
            throw new BPMWorkflowDefVerifyException(BPMBusinessExceptionCode.PAAS_FLOW_BPM_VERIFIY_FLOW_NOT_TYPE_RELATION_FIELD, userTask.getName());
        }
        //添加下查找关联的entityId
        userTask.getExtension().put(WorkflowKey.ActivityKey.ExtensionKey.lookupFieldEntityId, ((Map) entityFieldMap.get(fieldApiName)).get(BPMConstants.MetadataKey.targetApiName));
    }

}
